# 提高住院采集率
出院患者上传住院期间的药品关联追溯码数据上传医保局,提高采集率的功能实现方案

1.每日实时按照药房领药单进行采集领药单的药品追溯码,从拆零池获取药品追溯码进行用药的匹配保存;(目前业务已实现)
2.定时任务实时采集静配和手麻药品的追溯码,从拆零池获取药品追溯码进行用药的匹配保存;(参考阜阳人医的processInpatientDispenseTask定时任务实现)
3.出院时,按照出院患者的住院号,将明细中遗漏没有赋码的药品明细,再次进行赋码,将住院期间的所有药品追溯码数据上传医保局,提高采集率;(未实现)

# 功能目标
1.先实现静配药品追溯码的采集
2.按照出院结算后的患者,将没有采集追溯码的药品补码,再上传追溯码信息到3505接口

保存到3505业务表的数据,区分是否为住院或门诊

# 接口功能

1.处理指定时间段的住院拆零确认数据
2.处理指定时间段的出院患者数据


# 需要做的业务调整

1.门诊和住院的就诊流水号,如果没有为空,就不上传3505,在查询3505数据时,改为非空的查询判断;
2.取消表结构中的默认值'-'的填写,需要修改所有的历史数据进行矫正,才能保持所有项目的代码风格一致性;

更新敬佩和出院结算功能前,需要同步更新表结构!!