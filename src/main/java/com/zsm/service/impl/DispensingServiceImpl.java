package com.zsm.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsm.common.exception.BusinessException;
import com.zsm.config.HangChuangConfig;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.*;
import com.zsm.mapper.YsfStoDpsSubMapper;
import com.zsm.mapper.YsfStoTcStatusMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.dto.InpatientDispenseRecord;
import com.zsm.model.dto.InpatientPrescriptionQueryDto;
import com.zsm.model.enums.DispenseOrderStatusEnum;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.enums.SdTcStatusEnum;
import com.zsm.model.enums.StoTcTaskStatusEnum;
import com.zsm.model.enums.SyncAccountEnum;
import com.zsm.model.saas.request.ConfirmDispDrugDataRequest;
import com.zsm.model.saas.request.ConfirmDispDrugRequest;
import com.zsm.model.saas.request.GetTracCodgStoreDataRequest;
import com.zsm.model.saas.request.GetTracCodgStoreRequest;
import com.zsm.model.saas.response.AccessTokenReponse;
import com.zsm.model.saas.response.GetTracCodgStoreDataResponse;
import com.zsm.model.vo.*;
import com.zsm.service.*;
import com.zsm.utils.DateUtils;
import com.zsm.utils.HttpRequestUtil;
import com.zsm.utils.SaasHttpUtil;
import com.zsm.utils.SoapUtil;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发药服务实现类
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
@Service
public class DispensingServiceImpl implements DispensingService {

    /**
     * 批处理大小
     */
    private static final int BATCH_SIZE = 50;
    /**
     * 时间间隔（分钟）
     */
    private static final int TIME_INTERVAL_MINUTES = 5;
    /**
     * 重试次数
     */
    private static final int MAX_RETRY_TIMES = 3;

    @Resource
    private YsfStoTcStatusMapper ysfStoTcStatusMapper;
    @Resource
    private YsfStoDpsSubMapper ysfStoDpsSubMapper;
    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;
    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;
    @Resource
    private YsfStoTcService ysfStoTcService;
    @Resource
    private YsfStoTcStatusService ysfStoTcStatusService;
    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private FuYangRenYiAnsycService fuYangRenYiAnsycService;
    @Resource
    private YsfStoDpsSubService ysfStoDpsSubService;
    @Resource
    private YsfStoDpsSubHosService ysfStoDpsSubHosService;
    @Resource
    private YsfStoDpsService ysfStoDpsService;
    @Resource
    private HangChuangService hangChuangService;
    @Resource
    private HangChuangConfig hangChuangConfig;

    /**
     * 将列表按指定大小分割成多个子列表
     *
     * @param list      原始列表
     * @param batchSize 批次大小
     * @param <T>       列表元素类型
     * @return 分割后的子列表集合
     */
    private static <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        int size = list.size();

        for (int i = 0; i < size; i += batchSize) {
            int endIndex = Math.min(i + batchSize, size);
            batches.add(list.subList(i, endIndex));
        }

        return batches;
    }

    /**
     * 根据业务子ID获取发药明细信息
     *
     * @param bizSubId 业务子ID
     * @return 发药明细信息，如果未找到则返回null
     */
    @Override
    public DispensingDetailVo getDetailByBizSubId(String bizSubId) {
        // 查询与业务子ID关联的追溯码记录
        LambdaQueryWrapper<YsfStoTcStatus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(YsfStoTcStatus::getCfmxxh, bizSubId)
                .in(YsfStoTcStatus::getSdTcStatus, "3001", "3002"); // 门诊和住院发药类型

        List<YsfStoTcStatus> statusList = ysfStoTcStatusMapper.selectList(queryWrapper);

        if (statusList.isEmpty()) {
            return null;
        }

        // 构建发药明细信息
        DispensingDetailVo detailVo = new DispensingDetailVo();

        // 从第一条记录获取基本信息
        YsfStoTcStatus firstStatus = statusList.get(0);
        detailVo.setBizSubId(bizSubId);
        detailVo.setDispensingId(firstStatus.getIdBizOri()); // 主单ID通常在id_biz_ori字段
        // 这里模拟填充其他数据，实际应从数据库获取
        detailVo.setDispensingNo("DPS" + firstStatus.getIdBizOri());
        detailVo.setPrescriptionNo("RX" + (System.currentTimeMillis() % 10000));
        detailVo.setPatientId("PT" + (System.currentTimeMillis() % 10000));
        detailVo.setPatientName("患者" + bizSubId.substring(Math.max(0, bizSubId.length() - 4)));
        detailVo.setDrugCode(firstStatus.getDrugCode());
        detailVo.setDrugName("药品-" + firstStatus.getDrugCode());
        detailVo.setSpecification("规格-标准");
        detailVo.setUnit("盒");

        // 计算总数量（可能多条追溯码对应同一个明细）
        int totalQuantity = statusList.stream()
                .mapToInt(YsfStoTcStatus::getSelRetnCnt)
                .sum();
        detailVo.setQuantity(new BigDecimal(totalQuantity));

        detailVo.setPrice(new BigDecimal("10.50"));
        detailVo.setAmount(detailVo.getPrice().multiply(detailVo.getQuantity()));
        detailVo.setDispensingTime(firstStatus.getCreateTime());
        detailVo.setDispensingUser(firstStatus.getCreateBy());
        detailVo.setStorageId(firstStatus.getIdDept());
        detailVo.setStorageName("药房-" + firstStatus.getIdDept());

        // 设置追溯数量和追溯码
        detailVo.setTracedCount(statusList.size());
        List<String> traceCodes = statusList.stream()
                .map(YsfStoTcStatus::getDrugtracinfo)
                .collect(Collectors.toList());
        detailVo.setTraceCodes(traceCodes);

        return detailVo;
    }

    /**
     * 处理住院拆零确认数据
     * 按时间范围分批处理住院发药的拆零药品确认
     */
    @Override
    public void processInpatientDispenseConfirmation() {
        // 1. 计算时间范围（上一个小时）
        LocalDateTime endTime = LocalDateTime.now().withMinute(0).withSecond(0).withNano(0);
        LocalDateTime startTime = endTime.minusHours(1);

        log.info("开始处理住院拆零确认数据，时间范围: {} - {}", startTime, endTime);

        // 2. 按5分钟时间片段分批处理
        LocalDateTime currentStart = startTime;
        while (currentStart.isBefore(endTime)) {
            LocalDateTime currentEnd = currentStart.plusMinutes(TIME_INTERVAL_MINUTES);
            processTimeSlot(currentStart, currentEnd);
            currentStart = currentEnd;
        }

        log.info("住院拆零确认数据处理完成");
    }

    /**
     * 按指定时间范围处理住院拆零确认数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @Override
    public ApiResult<ProcessingResultVo> processInpatientDispenseConfirmationByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        // 1. 参数校验
        if (startTime == null || endTime == null) {
            log.error("处理住院拆零确认数据失败：开始时间或结束时间不能为空");
            throw new BusinessException("开始时间和结束时间不能为空");
        }

        if (startTime.isAfter(endTime)) {
            log.error("处理住院拆零确认数据失败：开始时间不能晚于结束时间，startTime: {}, endTime: {}", startTime, endTime);
            throw new BusinessException("开始时间不能晚于结束时间");
        }

        log.info("开始处理住院拆零确认数据，指定时间范围: {} - {}", startTime, endTime);

        int totalProcessedSlots = 0;
        int successfulSlots = 0;
        int failedSlots = 0;

        // 汇总各时间片段的业务统计数据
        int totalHisDataCount = 0;
        int totalSplitDrugCount = 0;
        int totalConfirmedCount = 0;
        int totalSkippedCount = 0;
        int totalFailedCount = 0;
        boolean overallSuccess = true;
        List<ProcessingResultVo.AccountProcessingDetail> aggregatedAccountDetails = new ArrayList<>();
        StringBuilder detailMessages = new StringBuilder();

        try {
            // 2. 按5分钟时间片段分批处理
            LocalDateTime currentStart = startTime;
            while (currentStart.isBefore(endTime)) {
                LocalDateTime currentEnd = currentStart.plusMinutes(TIME_INTERVAL_MINUTES);
                // 确保不超过结束时间
                if (currentEnd.isAfter(endTime)) {
                    currentEnd = endTime;
                }

                totalProcessedSlots++;

                try {
                    log.info("处理时间片段: {} - {}", currentStart, currentEnd);
                    ApiResult<ProcessingResultVo> slotResult = processTimeSlot(currentStart, currentEnd);

                    // 累加统计信息
                    if (slotResult != null && slotResult.getData() != null) {
                        ProcessingResultVo data = slotResult.getData();
                        totalHisDataCount += data.getHisDataCount() != null ? data.getHisDataCount() : 0;
                        totalSplitDrugCount += data.getSplitDrugCount() != null ? data.getSplitDrugCount() : 0;
                        totalConfirmedCount += data.getConfirmedCount() != null ? data.getConfirmedCount() : 0;
                        totalSkippedCount += data.getSkippedCount() != null ? data.getSkippedCount() : 0;
                        totalFailedCount += data.getFailedCount() != null ? data.getFailedCount() : 0;

                        if (data.getAccountDetails() != null) {
                            aggregatedAccountDetails.addAll(data.getAccountDetails());
                        }

                        if (!Boolean.TRUE.equals(data.getSuccess())) {
                            overallSuccess = false;
                        }
                    } else {
                        overallSuccess = false;
                    }

                    // 记录片段消息
                    if (slotResult != null) {
                        detailMessages.append(slotResult.getMsg()).append("; ");
                    }

                    successfulSlots++;
                    log.info("时间片段处理成功: {} - {}", currentStart, currentEnd);
                } catch (Exception e) {
                    failedSlots++;
                    overallSuccess = false;
                    log.error("时间片段处理失败: {} - {}, 错误信息: {}", currentStart, currentEnd, e.getMessage(), e);
                    // 继续处理下一个时间片段，不中断整个流程
                }

                currentStart = currentEnd;
            }

            log.info("住院拆零确认数据处理完成，时间范围: {} - {}, 总片段数: {}, 成功: {}, 失败: {}",
                    startTime, endTime, totalProcessedSlots, successfulSlots, failedSlots);

        } catch (Exception e) {
            log.error("处理住院拆零确认数据时发生系统异常，时间范围: {} - {}, 已处理片段: {}, 成功: {}, 失败: {}",
                    startTime, endTime, totalProcessedSlots, successfulSlots, failedSlots, e);
            throw new BusinessException("处理住院拆零确认数据时发生系统异常: " + e.getMessage(), e);
        }

        // 如果有失败的片段，记录警告
        if (failedSlots > 0) {
            log.warn("住院拆零确认数据处理部分失败，时间范围: {} - {}, 失败片段数: {}/{}",
                    startTime, endTime, failedSlots, totalProcessedSlots);
        }

        // 构建汇总结果
        ProcessingResultVo summary = ProcessingResultVo.builder()
                .success(overallSuccess)
                .message(overallSuccess ?
                        String.format("处理完成 - HIS获取:%d条, 拆零药品:%d条, 确认发药:%d条", totalHisDataCount, totalSplitDrugCount, totalConfirmedCount)
                        : "处理部分失败，详情见accountDetails")
                .hisDataCount(totalHisDataCount)
                .splitDrugCount(totalSplitDrugCount)
                .confirmedCount(totalConfirmedCount)
                .skippedCount(totalSkippedCount)
                .failedCount(totalFailedCount)
                .accountDetails(aggregatedAccountDetails)
                .build();

        String businessMessage = String.format("指定时间范围[%s - %s]处理结果: %s，时间片段总数:%d，成功:%d，失败:%d",
                startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                overallSuccess ? "成功" : "部分失败",
                totalProcessedSlots, successfulSlots, failedSlots);

        return overallSuccess ? ApiResult.success(businessMessage, summary) : ApiResult.error(500, businessMessage, summary);
    }

    /**
     * 处理指定时间段的数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 包含处理结果详情的API响应对象
     */
    @Override
    public ApiResult<ProcessingResultVo> processTimeSlot(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("开始处理时间段: {} - {}", startTime, endTime);

        // 初始化结果统计
        List<ProcessingResultVo.AccountProcessingDetail> accountDetails = new ArrayList<>();
        int totalHisDataCount = 0;
        int totalSplitDrugCount = 0;
        int totalConfirmedCount = 0;
        int totalSkippedCount = 0;
        int totalFailedCount = 0;
        boolean overallSuccess = true;
        StringBuilder errorMessages = new StringBuilder();
        StringBuilder detailMessages = new StringBuilder();

        try {
            // 1. 循环处理所有同步账号
            SyncAccountEnum.forEachAccount(account -> {
                ProcessingResultVo.AccountProcessingDetail accountDetail = ProcessingResultVo.AccountProcessingDetail.builder()
                        .username(account.getUsername())
                        .description(account.getDescription())
                        .success(true)
                        .hisDataCount(0)
                        .splitDrugCount(0)
                        .confirmedCount(0)
                        .build();

                try {
                    log.info("开始处理账号: {} - {}", account.getUsername(), account.getDescription());
                    AccessTokenReponse userInfo = SaasHttpUtil.getAccessToken(account.getUsername(), account.getPassword());
                    if (userInfo.getAuthorization() == null) {
                        log.error("获取帐号token信息失败,接口返回信息:{}", userInfo.getReturnMsg());
                        throw new BusinessException("获取帐号token信息失败,接口返回信息:" + userInfo.getReturnMsg());
                    }
                    SaasUserInfoResponse userInfoResponse = SaasHttpUtil.getInfo(userInfo.getAuthorization());

                    Long ymfUserId = userInfoResponse.getUser().getUserId();
                    // 2. 获取当前账号对应的病区id
                    String wardId = userInfo.getFyyfId();
                    accountDetail.setWardId(wardId);

                    // 3. 使用当前账号调用相关接口，并获取处理结果
                    // ProcessingResultVo.AccountProcessingDetail result = processAccountData(account, userInfo.getAuthorization(), startTime, endTime, wardId, ymfUserId);
                    ProcessingResultVo.AccountProcessingDetail result = processAccountDataOfHangChuang(account, userInfo.getAuthorization(), startTime, endTime, wardId, ymfUserId);

                    // 合并账号处理结果
                    accountDetail.setHisDataCount(result.getHisDataCount());
                    accountDetail.setSplitDrugCount(result.getSplitDrugCount());
                    accountDetail.setConfirmedCount(result.getConfirmedCount());
                    accountDetail.setSuccess(result.getSuccess());
                    accountDetail.setErrorMessage(result.getErrorMessage());

                    // 构建详细信息
                    detailMessages.append(String.format("[%s(%s)] 病区:%s HIS数据:%d条 拆零药品:%d条 确认发药:%d条",
                            account.getDescription(), account.getUsername(), wardId,
                            result.getHisDataCount(), result.getSplitDrugCount(), result.getConfirmedCount()));

                    if (!result.getSuccess()) {
                        detailMessages.append(" 失败原因:").append(result.getErrorMessage());
                    }
                    detailMessages.append("; ");

                    log.info("完成处理账号: {} - {}, HIS数据: {}, 拆零药品: {}, 确认数量: {}",
                            account.getUsername(), account.getDescription(),
                            result.getHisDataCount(), result.getSplitDrugCount(), result.getConfirmedCount());
                } catch (Exception e) {
                    accountDetail.setSuccess(false);
                    accountDetail.setErrorMessage(e.getMessage());
                    detailMessages.append(String.format("[%s(%s)] 处理失败: %s; ",
                            account.getDescription(), account.getUsername(), e.getMessage()));
                    log.error("处理账号失败: {} - {}, 错误信息: {}",
                            account.getUsername(), account.getDescription(), e.getMessage(), e);
                }

                accountDetails.add(accountDetail);
            });

            // 4. 汇总统计数据
            for (ProcessingResultVo.AccountProcessingDetail detail : accountDetails) {
                totalHisDataCount += detail.getHisDataCount();
                totalSplitDrugCount += detail.getSplitDrugCount();
                totalConfirmedCount += detail.getConfirmedCount();

                if (!detail.getSuccess()) {
                    overallSuccess = false;
                    totalFailedCount++;
                    if (errorMessages.length() > 0) {
                        errorMessages.append("; ");
                    }
                    errorMessages.append(detail.getUsername()).append(": ").append(detail.getErrorMessage());
                }
            }

        } catch (Exception e) {
            overallSuccess = false;
            String systemError = "系统异常: " + e.getMessage();
            errorMessages.append(systemError);
            detailMessages.append(systemError);
            log.error("处理时间段 {} - {} 失败", startTime, endTime, e);
        }

        // 5. 构建返回结果
        ProcessingResultVo resultDto = ProcessingResultVo.builder()
                .success(overallSuccess)
                .message(overallSuccess ?
                        String.format("处理完成 - HIS获取: %d条, 拆零药品: %d条, 确认发药: %d条",
                                totalHisDataCount, totalSplitDrugCount, totalConfirmedCount) :
                        "处理部分失败: " + errorMessages.toString())
                .hisDataCount(totalHisDataCount)
                .splitDrugCount(totalSplitDrugCount)
                .confirmedCount(totalConfirmedCount)
                .skippedCount(totalSkippedCount)
                .failedCount(totalFailedCount)
                .accountDetails(accountDetails)
                .build();

        // 6. 构建可读的业务执行结果消息
        String businessMessage = String.format("时间段[%s - %s]处理结果: %s 详细信息: %s",
                startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                overallSuccess ? "成功" : "部分失败",
                detailMessages.toString());

        // 7. 返回ApiResult包装的结果
        return overallSuccess ?
                ApiResult.success(businessMessage, resultDto) :
                ApiResult.error(500, businessMessage, resultDto);
    }

    /**
     * 处理指定账号的数据-东华接口
     *
     * @param account   同步账号枚举
     * @param token     访问令牌
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param wardId    病区ID
     * @return 账号处理结果详情
     */
    private ProcessingResultVo.AccountProcessingDetail processAccountData(SyncAccountEnum account, String token, LocalDateTime startTime,
                                                                          LocalDateTime endTime, String wardId, Long ymfUserId) {

        ProcessingResultVo.AccountProcessingDetail result = ProcessingResultVo.AccountProcessingDetail.builder()
                .username(account.getUsername())
                .description(account.getDescription())
                .wardId(wardId)
                .hisDataCount(0)
                .splitDrugCount(0)
                .confirmedCount(0)
                .success(true)
                .build();

        try {
            // 4. 调用东华接口获取住院领药数据（使用指定账号）
            List<InpatientDispenseRecord> records = getInpatientDispenseData(startTime, endTime, wardId);
            result.setHisDataCount(records != null ? records.size() : 0);

            if (CollectionUtils.isEmpty(records)) {
                log.info("时间段 {} - {} 无数据", startTime, endTime);
                return result;
            }

            // 4. 筛选拆零药品（trdn_flag=1）
            List<InpatientDispenseRecord> splitDrugRecords = records.stream()
                    .filter(record -> Integer.valueOf(1).equals(record.getTrdnFlag()))
                    .collect(Collectors.toList());
            result.setSplitDrugCount(splitDrugRecords.size());

            if (CollectionUtils.isEmpty(splitDrugRecords)) {
                log.info("时间段 {} - {} 无拆零药品", startTime, endTime);
                return result;
            }

            log.info("账号 {} 时间段 {} - {} 获取到拆零药品数量: {}",
                    account.getDescription(), startTime, endTime, splitDrugRecords.size());

            // 7. 分批处理拆零药品（传入账号信息）
            // int confirmedCount = processBatchRecords(account, token, splitDrugRecords, ymfUserId);
            // result.setConfirmedCount(confirmedCount);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            e.printStackTrace();
            log.error("处理账号 {}({}) 时间段 {} - {} 数据失败",
                    account.getDescription(), account.getUsername(), startTime, endTime, e);
            throw e; // 重新抛出异常，让上层捕获
        }

        return result;
    }

    /**
     * 调用东华接口获取住院领药数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param wardId    病区ID
     * @return 住院发药记录列表
     */
    private List<InpatientDispenseRecord> getInpatientDispenseData(LocalDateTime startTime, LocalDateTime endTime, String wardId) {
        log.info("调用东华接口获取病区 {} 的药品数据，时间范围: {} - {}",
                wardId, startTime, endTime);

        // 构建请求参数
        InpatientPrescriptionQueryDto queryDto = new InpatientPrescriptionQueryDto();
        queryDto.setDeptId(wardId);
        queryDto.setStartTime(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        queryDto.setEndTime(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        queryDto.setPatWardId("");
        queryDto.setRecordId("");
        queryDto.setFgDps("0"); // 0-发药

        try {
            // 调用SOAP接口
            final SoapResponseVo mes0272 = SoapUtil.callSoapServiceWithParams("MES0272", queryDto);
            final JSONObject jsonStr = mes0272.getJsonResult();

            Integer businessCode = jsonStr.getInt("code", -1);
            if (businessCode != 0) {
                String businessMessage = jsonStr.getStr("message", "业务执行失败");
                log.error("SOAP业务执行失败，业务状态码: {}, 错误信息: {}", businessCode, businessMessage);
                return new ArrayList<>();
            }

            // 将响应数据转换为List<InpatientDispenseRecord>
            Object dataList = jsonStr.get("dataList");
            if (dataList instanceof List) {
                List<Map<String, Object>> rawList = (List<Map<String, Object>>) dataList;
                return rawList.stream()
                        .map(item -> JSONUtil.toBean(JSONUtil.toJsonStr(item), InpatientDispenseRecord.class))
                        .collect(Collectors.toList());
            }

            return new ArrayList<>();
        } catch (Exception e) {
            log.error("调用东华接口失败", e);
            throw new BusinessException("调用东华接口失败: " + e.getMessage());
        }
    }

    /**
     * 分批处理记录数据
     *
     * @param account 同步账号枚举
     * @param token   访问令牌
     * @param records 记录列表
     * @return 实际确认处理的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int processBatchRecords(SyncAccountEnum account, String token, List<InPatientDispenseDetailBindScatteredVo> records, Long ymfUserId) {
        // 按批次大小分组处理
        List<List<InPatientDispenseDetailBindScatteredVo>> batches = partitionList(records, BATCH_SIZE);

        int totalConfirmed = 0;
        for (List<InPatientDispenseDetailBindScatteredVo> batch : batches) {
            int batchConfirmed = processSingleBatch(account, token, batch, ymfUserId);
            totalConfirmed += batchConfirmed;
        }

        return totalConfirmed;
    }

    /**
     * 处理单个批次的数据
     *
     * @param account 同步账号枚举
     * @param token   访问令牌
     * @param batch   批次数据
     * @return 实际确认处理的数量
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public int processSingleBatch(SyncAccountEnum account, String token, List<InPatientDispenseDetailBindScatteredVo> batch, Long ymfUserId) {
        try {
            log.info("开始处理账号 {} 的批次数据，数量: {}", account.getDescription(), batch.size());

            // 8. 调用SAAS接口获取追溯码信息（使用指定账号）
            List<InPatientDispenseDetailBindScatteredVo> enrichedBatch = hangChuangService.handleTracCodgStoreForInpatient(batch, token);
            // 7. 数据校验和重复处理检查
            enrichedBatch = filterAndValidateBatchData(enrichedBatch);

            // 如果过滤后批次为空，跳过后续处理
            if (CollectionUtils.isEmpty(enrichedBatch)) {
                log.info("批次数据全部已处理过，跳过后续操作");
                return 0;
            }

            // 8. 保存数据到本地数据库
            saveBatchData(enrichedBatch, account, ymfUserId);

            // 11. 调用SAAS确认接口（使用指定账号）
            confirmDispenseDrug(account, token, enrichedBatch);

            log.info("成功处理账号 {} 的一批拆零确认数据，数量: {}", account.getDescription(), enrichedBatch.size());

            return enrichedBatch.size();

        } catch (Exception e) {
            log.error("处理账号 {} 批次数据失败，数量: {}, 异常详情: {}", account.getDescription(), batch.size(), e.getMessage(), e);
            // 确保抛出RuntimeException以触发事务回滚
            if (e instanceof RuntimeException) {
                throw e;
            } else {
                throw new BusinessException("账号 " + account.getDescription() + " 批次处理失败: " + e.getMessage(), e);
            }
        }
    }


    /**
     * 调用SAAS确认接口
     *
     * @param account 同步账号枚举
     * @param token   访问令牌
     * @param batch   批次数据
     */
    private void confirmDispenseDrug(SyncAccountEnum account, String token, List<InPatientDispenseDetailBindScatteredVo> batch) {
        log.info("使用账号 {} 调用SAAS确认接口，批次数量: {}", account.getDescription(), batch.size());

        // 构建确认发药接口请求参数
        List<ConfirmDispDrugDataRequest> requestDataList = new ArrayList<>();
        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            if ("1".equals(record.getTrdnFlag())) {
                ConfirmDispDrugDataRequest request = new ConfirmDispDrugDataRequest();
                request.setDrugCode(record.getHisDrugCode());
                request.setDispCnt(record.getSelRetnCnt());
                request.setCfxh(record.getCfxh());
                request.setCfmxxh(record.getCfmxxh());
                requestDataList.add(request);
            }
        }

        if (!requestDataList.isEmpty()) {
            // 构建批次请求
            ConfirmDispDrugRequest confirmRequest = new ConfirmDispDrugRequest();
            confirmRequest.setDataList(requestDataList);

            // 调用confirmDispDrug接口
            SaasHttpUtil.confirmDispDrug(token, confirmRequest);
        }
    }

    /**
     * 过滤重复数据并进行校验
     *
     * @param batch 批次数据
     * @return 过滤和校验后的批次数据
     */
    private List<InPatientDispenseDetailBindScatteredVo> filterAndValidateBatchData(List<InPatientDispenseDetailBindScatteredVo> batch) {
        // 1. 重复处理检查 - 根据cfmxxh查询已处理的数据
        List<String> cfmxxhList = batch.stream()
                .map(InPatientDispenseDetailBindScatteredVo::getCfmxxh)
                .collect(Collectors.toList());

        // 查询住院发药明细表（ysf_sto_dps_sub_hos）中已处理的数据
        LambdaQueryWrapper<YsfStoDpsSubHos> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(YsfStoDpsSubHos::getCfmxxh, cfmxxhList);
        List<YsfStoDpsSubHos> processedRecords = ysfStoDpsSubHosService.list(queryWrapper);

        Set<String> processedCfmxxhs = processedRecords.stream()
                .map(YsfStoDpsSubHos::getCfmxxh)
                .collect(Collectors.toSet());

        // 2. 过滤掉已处理的数据
        List<InPatientDispenseDetailBindScatteredVo> filteredBatch = batch.stream()
                .filter(record -> !processedCfmxxhs.contains(record.getCfmxxh()))
                .collect(Collectors.toList());

        log.info("原批次数量: {}, 已处理数量: {}, 待处理数量: {}",
                batch.size(), processedCfmxxhs.size(), filteredBatch.size());

        // 3. 对过滤后的数据进行基础校验，只保留通过校验的数据
        List<InPatientDispenseDetailBindScatteredVo> validatedBatch = new ArrayList<>();
        for (InPatientDispenseDetailBindScatteredVo record : filteredBatch) {
            try {
                validateSingleRecord(record);
                validatedBatch.add(record);
            } catch (BusinessException e) {
                log.warn("记录校验失败，跳过处理: cfmxxh={}, 原因: {}", record.getCfmxxh(), e.getMessage());
            }
        }

        log.info("校验结果 - 原数量: {}, 校验通过: {}, 校验失败: {}",
                filteredBatch.size(), validatedBatch.size(), filteredBatch.size() - validatedBatch.size());

        return validatedBatch;
    }

    /**
     * 单条记录校验
     *
     * @param record 发药记录
     * @throws BusinessException 当记录数据不符合要求时抛出业务异常
     */
    private void validateSingleRecord(InPatientDispenseDetailBindScatteredVo record) {
        if (ObjectUtils.isEmpty(record.getRecordId())) {
            throw new BusinessException("发药记录ID不能为空");
        }
        if (ObjectUtils.isEmpty(record.getFixmedinsHilistId())) {
            throw new BusinessException("药品编码不能为空");
        }
        if (ObjectUtils.isEmpty(record.getSelRetnCnt()) || record.getSelRetnCnt() <= 0) {
            throw new BusinessException("发药数量必须大于0");
        }
        if (ObjectUtils.isEmpty(record.getCfxh()) || ObjectUtils.isEmpty(record.getCfmxxh())) {
            throw new BusinessException("处方序号和明细序号不能为空");
        }

        // 验证追溯码
        // if (Integer.valueOf(1).equals(record.getTrdnFlag())) {
        //     if (CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
        //         throw new BusinessException("拆零药品追溯码不能为空");
        //     }
        // }
    }

    /**
     * 保存批次数据到本地数据库
     *
     * @param batch 批次数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchData(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, Long ymfUserId) {
        log.info("开始保存批次数据到本地数据库，数量: {}", batch.size());

        // 1. 保存发药单主表（ysf_sto_dps）
        List<YsfStoDps> dpsList = new ArrayList<>();
        Map<String, YsfStoDps> dpsMap = new HashMap<>(); // 用于后续关联

        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            // 检查是否已存在相同cfxh的发药单
            if (!dpsMap.containsKey(record.getCfxh())) {
                YsfStoDps dps = buildDps(record, account);
                dpsList.add(dps);
                dpsMap.put(record.getCfxh(), dps);
            }
        }

        // 批量插入发药单
        ysfStoDpsService.saveBatch(dpsList);

        final NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

        // 2. 保存发药单明细表（ysf_sto_dps_sub_hos）
        List<YsfStoDpsSubHos> dpsSubHosList = new ArrayList<>();
        Map<String, YsfStoDpsSubHos> dpsSubHosMap = new HashMap<>(); // 用于后续关联

        // 批量保存后，dpsList中的对象已经有了自增ID
        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            YsfStoDps dps = dpsMap.get(record.getCfxh());
            YsfStoDpsSubHos dpsSubHos = buildDpsSubHos(record, dps, account);
            // 确保设置了主表ID（批量保存后已回填）
            if (dps.getIdDps() != null && dpsSubHos.getIdDps() == null) {
                dpsSubHos.setIdDps(dps.getIdDps().toString());
            }
            dpsSubHosList.add(dpsSubHos);
            dpsSubHosMap.put(record.getCfmxxh(), dpsSubHos); // 使用cfmxxh作为key
        }
        ysfStoDpsSubHosService.saveBatch(dpsSubHosList);

        // 定时任务处理拆零发药,保存扫码任务是多余的操作,强行增加的业务复杂度
        // 3. 保存扫码任务表
        // List<YsfStoTcTask> tcTaskList = new ArrayList<>();
        // Map<String, YsfStoTcTask> taskMap = new HashMap<>(); // 用于后续关联

        // for (InPatientDispenseDetailBindScatteredVo record : batch) {
        //     // 每个cfxh对应一个任务
        //     if (!taskMap.containsKey(record.getCfxh())) {
        //         YsfStoTcTask tcTask = buildTcTask(record, account, nhsaAccount);
        //         tcTask.setSdTcStatus(SdTcStatusEnum.INPATIENT_DISPENSING.getCode());
        //         tcTask.setFgStatus(StoTcTaskStatusEnum.COMPLETED.getCode());
        //         tcTaskList.add(tcTask);
        //         taskMap.put(record.getCfxh(), tcTask);
        //     }
        // }
        // ysfStoTcTaskService.saveBatch(tcTaskList);

        // 更新发药单关联的任务ID
        // for (Map.Entry<String, YsfStoDps> entry : dpsMap.entrySet()) {
        //     String cfxh = entry.getKey();
        //     YsfStoDps dps = entry.getValue();
        //     YsfStoTcTask task = taskMap.get(cfxh);
        //     if (task != null) {
        //         dps.setIdTask(task.getIdTask());
        //         ysfStoDpsService.updateById(dps);
        //     }
        // }

        // 4. 保存任务明细表
        // List<YsfStoTcTaskSub> tcTaskSubList = new ArrayList<>();
        // // 需要在tcTaskList批量保存后处理，确保任务ID已生成
        // for (InPatientDispenseDetailBindScatteredVo record : batch) {
        //     YsfStoTcTask tcTask = taskMap.get(record.getCfxh());
        //     YsfStoDpsSubHos dpsSubHos = dpsSubHosMap.get(record.getCfmxxh());
        //
        //     YsfStoTcTaskSub tcTaskSub = buildTcTaskSub(tcTask, record, account);
        //     // 设置id_biz_sub为ysf_sto_dps_sub_hos表的id字段值（批量保存后已有ID）
        //     if (dpsSubHos != null && dpsSubHos.getId() != null) {
        //         tcTaskSub.setIdBizSub(dpsSubHos.getId());
        //     }
        //     // 确保设置了任务ID（批量保存后已回填）
        //     if (tcTask != null && tcTask.getIdTask() != null && tcTaskSub.getIdTask() == null) {
        //         tcTaskSub.setIdTask(tcTask.getIdTask().toString());
        //     }
        //     tcTaskSub.setFgScanned(StoTcTaskStatusEnum.COMPLETED.getCode());
        //     tcTaskSub.setScanUser(account.getUsername());
        //     tcTaskSub.setScanTime(LocalDateTime.now());
        //     tcTaskSubList.add(tcTaskSub);
        // }
        // ysfStoTcTaskSubService.saveBatch(tcTaskSubList);

        // 5. 处理追溯码数据
        processTraceCodeData(batch, account, nhsaAccount);

        // 6. 更新3505表中的批号、有效期、批次号信息
        updateNhsa3505BatchInfo(batch, account, ymfUserId);

        log.info("成功保存批次数据到本地数据库，数量: {}", batch.size());
    }

    /**
     * 构建发药单对象（ysf_sto_dps）
     *
     * @param record  发药记录
     * @param account 同步账号枚举
     * @return 发药单对象
     */
    private YsfStoDps buildDps(InPatientDispenseDetailBindScatteredVo record, SyncAccountEnum account) {
        YsfStoDps dps = new YsfStoDps();

        // 基本信息
        dps.setCfxh(record.getCfxh());
        dps.setSdDps("1"); // 1:住院
        dps.setPatientId(record.getPatInHosId());
        dps.setIdPi(record.getPatInHosId());
        dps.setPsnName(record.getPsnName());
        dps.setPatInHosId(record.getPatInHosId());
        dps.setPatWardId(record.getPatWardId());
        dps.setPatWardName(record.getPatWardName());

        // 发药信息
        dps.setSendTime(DateUtils.parseDateTime(record.getSendTime()));
        dps.setIdDept(record.getDeptId());
        dps.setSelRetnOpterName(record.getPharName());
        dps.setSelRetnOpterId(record.getPharPracCertNo());

        // 状态信息
        dps.setFgStatus(DispenseOrderStatusEnum.DISPENSED.getCode()); // 已发药
        dps.setFgDps("0"); // 0:发药
        dps.setFgPrint("0"); // 未打印

        // 审计字段
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        dps.setIdOrg(nhsaAccount.getMedicalCode());
        dps.setOrgId(nhsaAccount.getMedicalCode());
        dps.setOrgName(nhsaAccount.getMedicalName());
        dps.setCreateTime(LocalDateTime.now());
        dps.setUpdateTime(LocalDateTime.now());
        dps.setCreateBy(account.getUsername());
        dps.setUpdateBy(account.getUsername());
        dps.setDelFlag("0");

        return dps;
    }

    /**
     * 构建发药单明细对象（ysf_sto_dps_sub_hos）
     *
     * @param record  发药记录
     * @param dps     发药单主表对象
     * @param account 同步账号枚举
     * @return 发药单明细对象
     */
    private YsfStoDpsSubHos buildDpsSubHos(InPatientDispenseDetailBindScatteredVo record, YsfStoDps dps, SyncAccountEnum account) {
        YsfStoDpsSubHos dpsSubHos = new YsfStoDpsSubHos();

        // 关联信息
        dpsSubHos.setCfmxxh(record.getCfmxxh());
        dpsSubHos.setCfxh(record.getCfxh());
        // 注意：此时dps.getIdDps()可能为null，需要在批量保存后更新
        if (dps.getIdDps() != null) {
            dpsSubHos.setIdDps(dps.getIdDps().toString());
        }
        dpsSubHos.setIdFee(record.getIdFee());
        dpsSubHos.setNaFee(record.getNaFee());

        // 药品信息
        dpsSubHos.setDrugCode(record.getFixmedinsHilistId());

        // 数量信息
        dpsSubHos.setSelRetnCnt(record.getSelRetnCnt());
        dpsSubHos.setQuantity(record.getSelRetnCnt());
        dpsSubHos.setUnit(record.getSelRetnUnit());
        dpsSubHos.setUnitSale(record.getSelRetnUnit());

        // 追溯码信息
        if ("1".equals(record.getTrdnFlag()) && !CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
            dpsSubHos.setDrugtracinfo(String.join(",", record.getDrugTracCodgs()));
            dpsSubHos.setTracCnt(record.getDrugTracCodgs().size());
        } else {
            dpsSubHos.setTracCnt(0);
        }

        // 患者信息
        dpsSubHos.setPatInHosId(record.getPatInHosId());
        dpsSubHos.setPatientId(record.getPatInHosId());
        dpsSubHos.setPsnName(record.getPsnName());

        // 审计字段
        NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
        dpsSubHos.setIdOrg(nhsaAccount.getMedicalCode());
        dpsSubHos.setOrgId(nhsaAccount.getMedicalCode());
        dpsSubHos.setOrgName(nhsaAccount.getMedicalName());
        dpsSubHos.setCreateTime(LocalDateTime.now());
        dpsSubHos.setUpdateTime(LocalDateTime.now());
        dpsSubHos.setCreateBy(account.getUsername());
        dpsSubHos.setUpdateBy(account.getUsername());
        dpsSubHos.setDelFlag("0");

        return dpsSubHos;
    }

    /**
     * 更新3505表中的批号、有效期、批次号信息
     *
     * @param batch   批次数据
     * @param account 同步账号枚举
     */
    private void updateNhsa3505BatchInfo(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, Long ymfUserId) {
        log.info("开始处理3505表批次信息，记录数: {}", batch.size());

        int insertCount = 0;
        int skipCount = 0;
        List<Nhsa3505> saveList = new ArrayList<>();

        // 1. 批量查询已存在的cfmxxh
        List<String> cfmxxhList = batch.stream()
                .map(InPatientDispenseDetailBindScatteredVo::getCfmxxh)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        Set<String> existingCfmxxhs = new HashSet<>();
        if (!cfmxxhList.isEmpty()) {
            LambdaQueryWrapper<Nhsa3505> existingQueryWrapper = new LambdaQueryWrapper<>();
            existingQueryWrapper.in(Nhsa3505::getCfmxxh, cfmxxhList)
                    .eq(Nhsa3505::getDeleteFlag, "0")
                    .select(Nhsa3505::getCfmxxh);

            List<Nhsa3505> existingRecords = nhsa3505Service.list(existingQueryWrapper);
            existingCfmxxhs = existingRecords.stream()
                    .map(Nhsa3505::getCfmxxh)
                    .collect(Collectors.toSet());
        }

        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            try {
                // 1. 检查必要字段
                if (StringUtils.isBlank(record.getCfmxxh())) {
                    log.warn("处方明细序号为空，跳过该条记录");
                    skipCount++;
                    continue;
                }

                // 3. 检查是否已存在记录，如果存在则跳过
                if (existingCfmxxhs.contains(record.getCfmxxh())) {
                    log.info("3505表记录已存在，跳过处理: cfmxxh={}", record.getCfmxxh());
                    skipCount++;
                    continue;
                }

                // 4. 构建新记录（参考save3505Async的转换逻辑）
                Nhsa3505 new3505 = new Nhsa3505();

                // 设置机构信息
                NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();
                new3505.setMedicalCode(nhsaAccount.getMedicalCode());
                new3505.setMedicalName(nhsaAccount.getMedicalName());

                // 基本药品信息
                new3505.setMedListCodg(record.getMedListCodg());
                new3505.setFixmedinsHilistId(record.getFixmedinsHilistId());
                new3505.setFixmedinsHilistName(record.getFixmedinsHilistName());
                new3505.setFixmedinsBchno(record.getFixmedinsBchno());

                // 医师信息
                new3505.setPrscDrCertno(record.getPrscDrCertno());
                new3505.setPrscDrName(record.getPrscDrName());

                // 药师信息
                new3505.setPharCertno(record.getPharCertno());
                new3505.setPharName(record.getPharName());
                new3505.setPharPracCertNo(record.getPharPracCertNo());

                // 患者就诊信息
                new3505.setMdtrtSn(record.getMdtrtSn());
                new3505.setPsnName(record.getPsnName());

                // 生产信息（重点保留的4个字段）
                new3505.setManuLotnum(record.getManuLotnum());
                if (StringUtils.isNotBlank(record.getManuDate())) {
                    new3505.setManuDate(DateUtils.parseDate(record.getManuDate()));
                }
                if (StringUtils.isNotBlank(record.getExpyEnd())) {
                    new3505.setExpyEnd(DateUtils.parseDate(record.getExpyEnd()));
                }
                new3505.setBchno(record.getBchno());

                // 标志信息
                new3505.setRxFlag(record.getRxFlag());
                new3505.setTrdnFlag(record.getTrdnFlag());

                // 处方信息
                new3505.setRtalDocno(record.getRtalDocno());
                new3505.setStooutNo(record.getStooutNo());
                new3505.setDrugTracInfo(String.join(",", record.getDrugTracCodgs()));

                // 销售信息
                new3505.setSelRetnCnt(new BigDecimal(record.getSelRetnCnt()));
                if (StringUtils.isNotBlank(record.getSelRetnTime())) {
                    new3505.setSelRetnTime(DateUtils.parseDateTime(record.getSelRetnTime()));
                }
                new3505.setSelRetnOpterName(record.getPharName());
                new3505.setMdtrtSetlType(record.getMdtrtSetlType());
                new3505.setHisEntpName(record.getProdentpName());
                // 处方序号信息
                new3505.setCfxh(record.getCfxh());
                new3505.setCfmxxh(record.getCfmxxh());
                new3505.setPatientId(record.getPatInHosId());

                // 系统信息
                LocalDateTime now = LocalDateTime.now();
                new3505.setCreateTime(now);
                new3505.setCreateDate(now.toLocalDate());
                new3505.setCreateBy(account.getUsername());
                new3505.setUpdateTime(now);
                new3505.setUpdateBy(account.getUsername());
                new3505.setDeleteFlag("0");
                new3505.setHsaSyncStatus("0"); // 未同步状态

                new3505.setYmfUserName(account.getUsername());
                new3505.setYmfNickName(account.getDescription());
                new3505.setYmfUserId(ymfUserId);

                new3505.setDeptId(record.getDeptId());
                // 发药单类型
                new3505.setSdDps(SdDpsEnum.INPATIENT);

                // 设置必填字段的默认值（如果为空）
                setDefaultValues(new3505);

                saveList.add(new3505);

            } catch (Exception e) {
                log.error("处理3505表批次信息异常，cfmxxh: {}", record.getCfmxxh(), e);
                skipCount++;
            }
        }

        // 5. 批量保存新记录
        if (!saveList.isEmpty()) {
            boolean result = nhsa3505Service.saveBatch(saveList);
            if (result) {
                insertCount = saveList.size();
                log.info("3505表批次信息批量保存成功，新增: {}条", insertCount);
            } else {
                log.error("3505表批次信息批量保存失败");
            }
        }

        log.info("3505表批次信息处理完成 - 新增: {}条, 跳过: {}条", insertCount, skipCount);
    }


    /**
     * 设置3505记录的默认值
     *
     * @param nhsa3505 3505记录对象
     */
    private void setDefaultValues(Nhsa3505 nhsa3505) {
        if (!StringUtils.isNotBlank(nhsa3505.getPrscDrName())) {
            nhsa3505.setPrscDrName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getPharName())) {
            nhsa3505.setPharName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getPsnName())) {
            nhsa3505.setPsnName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getSelRetnOpterName())) {
            nhsa3505.setSelRetnOpterName("-");
        }
        if (!StringUtils.isNotBlank(nhsa3505.getHisUniqueKey())) {
            nhsa3505.setHisUniqueKey(nhsa3505.getFixmedinsBchno());
        }
        if (!StringUtils.isNotBlank(nhsa3505.getCompositeKey())) {
            nhsa3505.setCompositeKey(nhsa3505.getFixmedinsBchno());
        }
    }

    /**
     * 构建扫码任务对象
     *
     * @param record      发药记录
     * @param account     同步账号枚举
     * @param nhsaAccount nhsa帐户
     * @return 扫码任务对象
     */
    private YsfStoTcTask buildTcTask(InPatientDispenseDetailBindScatteredVo record, SyncAccountEnum account, NhsaAccount nhsaAccount) {
        YsfStoTcTask tcTask = new YsfStoTcTask();

        // 业务信息
        tcTask.setCdBiz(record.getCfxh());
        tcTask.setFgPriority("1"); // 普通优先级
        tcTask.setIdUser(account.getUsername());
        tcTask.setIdDept(record.getPatWardId());
        tcTask.setSdTaskType("2");

        // 设置机构信息
        tcTask.setIdOrg(nhsaAccount.getMedicalCode());
        tcTask.setOrgId(nhsaAccount.getMedicalCode());
        tcTask.setOrgName(nhsaAccount.getMedicalName());

        // 审计字段
        tcTask.setCreateTime(LocalDateTime.now());
        tcTask.setUpdateTime(LocalDateTime.now());
        tcTask.setCreateBy(account.getUsername());
        tcTask.setUpdateBy(account.getUsername());
        tcTask.setDelFlag("0");
        tcTask.setRemark(record.getPsnName());

        return tcTask;
    }

    /**
     * 构建任务明细对象
     *
     * @param tcTask  扫码任务对象
     * @param record  发药记录
     * @param account 同步账号枚举
     * @return 任务明细对象
     */
    private YsfStoTcTaskSub buildTcTaskSub(YsfStoTcTask tcTask, InPatientDispenseDetailBindScatteredVo record, SyncAccountEnum account) {
        YsfStoTcTaskSub tcTaskSub = new YsfStoTcTaskSub();

        // 关联信息
        // 注意：此时tcTask.getIdTask()可能为null，需要在批量保存后更新
        if (tcTask.getIdTask() != null) {
            tcTaskSub.setIdTask(tcTask.getIdTask().toString());
        }
        tcTaskSub.setCfmxxh(record.getCfmxxh());

        // 药品信息
        tcTaskSub.setDrugCode(record.getHisDrugCode());
        // 安全处理追溯码信息
        if ("1".equals(record.getTrdnFlag()) && !CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
            tcTaskSub.setDrugtracinfo(String.join(",", record.getDrugTracCodgs()));
            tcTaskSub.setTracCnt(record.getDrugTracCodgs().size());
        } else {
            tcTaskSub.setDrugtracinfo("");
            tcTaskSub.setTracCnt(0);
        }
        tcTaskSub.setQuantity(record.getSelRetnCnt());
        tcTaskSub.setUnit(record.getSelRetnUnit());


        // 设置机构信息
        tcTaskSub.setIdOrg(tcTask.getIdOrg());
        tcTaskSub.setOrgId(tcTask.getIdOrg());
        tcTaskSub.setOrgName(tcTask.getOrgName());
        // 审计字段
        tcTaskSub.setCreateTime(LocalDateTime.now());
        tcTaskSub.setUpdateTime(LocalDateTime.now());
        tcTaskSub.setCreateBy(account.getUsername());
        tcTaskSub.setUpdateBy(account.getUsername());
        tcTaskSub.setDelFlag("0");
        tcTaskSub.setRemark(record.getPsnName());

        return tcTaskSub;
    }

    /**
     * 处理追溯码数据（批量优化版本）
     *
     * @param batch   批次数据
     * @param account 同步账号枚举
     */
    private void processTraceCodeData(List<InPatientDispenseDetailBindScatteredVo> batch, SyncAccountEnum account, NhsaAccount nhsaAccount) {
        log.info("开始批量处理追溯码数据，记录数: {}", batch.size());

        // 1. 收集所有需要处理的追溯码信息
        List<TraceCodeProcessInfo> traceCodeInfos = new ArrayList<>();
        for (InPatientDispenseDetailBindScatteredVo record : batch) {
            if ("1".equals(record.getTrdnFlag()) && !CollectionUtils.isEmpty(record.getDrugTracCodgs())) {
                for (String traceCode : record.getDrugTracCodgs()) {
                    TraceCodeProcessInfo info = new TraceCodeProcessInfo();
                    info.setTraceCode(traceCode);
                    info.setRecord(record);
                    traceCodeInfos.add(info);
                }
            }
        }

        if (traceCodeInfos.isEmpty()) {
            log.info("没有需要处理的追溯码数据");
            return;
        }

        log.info("需要处理的追溯码总数: {}", traceCodeInfos.size());

        // 2. 批量查询已存在的追溯码
        List<String> allTraceCodes = traceCodeInfos.stream()
                .map(TraceCodeProcessInfo::getTraceCode)
                .distinct()
                .collect(Collectors.toList());

        Map<String, YsfStoTc> existingTcMap = batchQueryExistingTraceCodes(allTraceCodes);

        // 3. 分离新增和更新的追溯码
        List<YsfStoTc> newTcList = new ArrayList<>();
        List<YsfStoTc> updateTcList = new ArrayList<>();
        List<YsfStoTcStatus> newStatusList = new ArrayList<>(); // 对应新增追溯码的状态记录
        List<YsfStoTcStatus> existingStatusList = new ArrayList<>(); // 对应已存在追溯码的状态记录

        LocalDateTime now = LocalDateTime.now();

        for (TraceCodeProcessInfo info : traceCodeInfos) {
            String traceCode = info.getTraceCode();
            InPatientDispenseDetailBindScatteredVo record = info.getRecord();

            YsfStoTc existingTc = existingTcMap.get(traceCode);

            if (existingTc == null) {
                // 创建新的追溯码记录
                YsfStoTc newTc = buildNewTraceCode(traceCode, record, account, nhsaAccount, now);
                newTcList.add(newTc);

                // 创建对应的状态记录（idTc暂时为null，批量保存后再设置）
                YsfStoTcStatus status = buildTraceCodeStatus(traceCode, record, account, null, nhsaAccount, now);
                newStatusList.add(status);
            } else {
                // 更新已存在的追溯码
                existingTc.setUpdateTime(now);
                existingTc.setUpdateBy(account.getUsername());
                updateTcList.add(existingTc);

                // 创建状态记录
                YsfStoTcStatus status = buildTraceCodeStatus(traceCode, record, account, existingTc.getIdTc(), nhsaAccount, now);
                existingStatusList.add(status);
            }
        }

        // 4. 批量保存新增的追溯码
        if (!newTcList.isEmpty()) {
            log.info("批量保存新增追溯码记录，数量: {}", newTcList.size());
            boolean saveResult = ysfStoTcService.saveBatch(newTcList);
            if (!saveResult) {
                throw new BusinessException("批量保存追溯码记录失败");
            }

            // 5. 更新状态记录中的idTc字段（按索引对应关系）
            for (int i = 0; i < newTcList.size() && i < newStatusList.size(); i++) {
                YsfStoTc newTc = newTcList.get(i);
                YsfStoTcStatus status = newStatusList.get(i);
                status.setIdTc(newTc.getIdTc());
            }
        }

        // 6. 批量更新已存在的追溯码
        if (!updateTcList.isEmpty()) {
            log.info("批量更新已存在追溯码记录，数量: {}", updateTcList.size());
            boolean updateResult = ysfStoTcService.updateBatchById(updateTcList);
            if (!updateResult) {
                throw new BusinessException("批量更新追溯码记录失败");
            }
        }

        // 7. 批量保存状态记录
        List<YsfStoTcStatus> allStatusList = new ArrayList<>();
        allStatusList.addAll(newStatusList);
        allStatusList.addAll(existingStatusList);

        if (!allStatusList.isEmpty()) {
            log.info("批量保存追溯码状态记录，数量: {}", allStatusList.size());
            boolean statusResult = ysfStoTcStatusService.saveBatch(allStatusList);
            if (!statusResult) {
                throw new BusinessException("批量保存追溯码状态记录失败");
            }
        }

        log.info("批量处理追溯码数据完成 - 新增: {}条, 更新: {}条, 状态记录: {}条",
                newTcList.size(), updateTcList.size(), allStatusList.size());
    }

    /**
     * 批量查询已存在的追溯码
     *
     * @param traceCodes 追溯码列表
     * @return 追溯码映射表
     */
    private Map<String, YsfStoTc> batchQueryExistingTraceCodes(List<String> traceCodes) {
        if (CollectionUtils.isEmpty(traceCodes)) {
            return new HashMap<>();
        }

        // 分批查询，避免IN条件过多
        Map<String, YsfStoTc> resultMap = new HashMap<>();
        List<List<String>> batches = partitionList(traceCodes, 1000); // 每批最多1000个

        for (List<String> batch : batches) {
            LambdaQueryWrapper<YsfStoTc> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(YsfStoTc::getDrugtracinfo, batch)
                    .eq(YsfStoTc::getDelFlag, "0");

            List<YsfStoTc> existingList = ysfStoTcService.list(queryWrapper);
            Map<String, YsfStoTc> batchMap = existingList.stream()
                    .collect(Collectors.toMap(YsfStoTc::getDrugtracinfo, Function.identity(), (o, n) -> n));

            resultMap.putAll(batchMap);
        }

        return resultMap;
    }

    /**
     * 构建新的追溯码记录
     *
     * @param traceCode   追溯码
     * @param record      发药记录
     * @param account     同步账号枚举
     * @param nhsaAccount nhsa账户
     * @param now         当前时间
     * @return 追溯码记录
     */
    private YsfStoTc buildNewTraceCode(String traceCode, InPatientDispenseDetailBindScatteredVo record,
                                       SyncAccountEnum account, NhsaAccount nhsaAccount, LocalDateTime now) {
        YsfStoTc newTc = new YsfStoTc();
        newTc.setDrugtracinfo(traceCode);
        newTc.setDrugCode(record.getHisDrugCode());
        newTc.setManuLotnum(record.getManuLotnum());

        // 解析有效期日期（格式：yyyy-MM-dd）
        if (StringUtils.isNotBlank(record.getExpyEnd())) {
            try {
                LocalDate expyDate = LocalDate.parse(record.getExpyEnd());
                newTc.setExpyEnd(expyDate.atStartOfDay());
            } catch (Exception e) {
                log.error("解析有效期失败: {}, 错误: {}", record.getExpyEnd(), e.getMessage());
            }
        }

        // 解析生产日期
        if (StringUtils.isNotBlank(record.getManuDate())) {
            try {
                newTc.setManuDate(DateUtils.parseDateTime(record.getManuDate()));
            } catch (Exception e) {
                log.error("解析生产日期失败: {}, 错误: {}", record.getManuDate(), e.getMessage());
            }
        }

        if (StringUtils.isNotBlank(record.getHisConRatio())) {
            newTc.setUnitSaleFactor(Integer.valueOf(record.getHisConRatio()));
            newTc.setUnitTc(record.getHisDosUnit());
        }

        newTc.setFgActive("1"); // 有效
        newTc.setIdDept(record.getPatWardId());

        // 设置机构信息
        newTc.setIdOrg(nhsaAccount.getMedicalCode());
        newTc.setOrgId(nhsaAccount.getMedicalCode());
        newTc.setOrgName(nhsaAccount.getMedicalName());
        newTc.setSdTcManage("简易管理");

        // 审计字段
        newTc.setCreateTime(now);
        newTc.setUpdateTime(now);
        newTc.setCreateBy(account.getUsername());
        newTc.setUpdateBy(account.getUsername());
        newTc.setDelFlag("0");

        return newTc;
    }

    /**
     * 构建追溯码状态记录
     *
     * @param traceCode   追溯码
     * @param record      发药记录
     * @param account     同步账号枚举
     * @param idTc        追溯码ID
     * @param nhsaAccount nhsa账户
     * @param now         当前时间
     * @return 追溯码状态记录
     */
    private YsfStoTcStatus buildTraceCodeStatus(String traceCode, InPatientDispenseDetailBindScatteredVo record,
                                                SyncAccountEnum account, Long idTc,
                                                NhsaAccount nhsaAccount, LocalDateTime now) {
        YsfStoTcStatus status = new YsfStoTcStatus();

        // 基本信息
        status.setSdTcStatus(SdTcStatusEnum.INPATIENT_DISPENSING.getCode());
        status.setSdTcManage("1"); // 简易管理模式
        status.setIdBizOri(record.getRecordDetailId());
        status.setCfmxxh(record.getCfmxxh());
        status.setDrugCode(record.getHisDrugCode());
        status.setDrugtracinfo(traceCode);
        status.setSdTc("2"); // 商品追溯码
        status.setSelRetnCnt(record.getSelRetnCnt());
        status.setFgUp("0"); // 未上传
        status.setFgActive("1"); // 有效
        status.setIdDept(record.getPatWardId());
        status.setIdTc(idTc);

        // 设置机构信息
        status.setIdOrg(nhsaAccount.getMedicalCode());
        status.setOrgId(nhsaAccount.getMedicalCode());
        status.setOrgName(nhsaAccount.getMedicalName());

        // 审计字段
        status.setCreateTime(now);
        status.setUpdateTime(now);
        status.setCreateBy(account.getUsername());
        status.setUpdateBy(account.getUsername());
        status.setDelFlag("0");

        return status;
    }

    /**
     * 追溯码处理信息内部类
     */
    @Data
    private static class TraceCodeProcessInfo {
        /**
         * 追溯码
         */
        private String traceCode;
        /**
         * 发药记录
         */
        private InPatientDispenseDetailBindScatteredVo record;
    }

    /**
     * 处理指定账号的数据-东华接口
     *
     * @param account   同步账号枚举
     * @param token     访问令牌
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param wardId    病区ID
     * @return 账号处理结果详情
     */
    private ProcessingResultVo.AccountProcessingDetail processAccountDataOfHangChuang(SyncAccountEnum account, String token, LocalDateTime startTime,
                                                                          LocalDateTime endTime, String wardId, Long ymfUserId) {

        ProcessingResultVo.AccountProcessingDetail result = ProcessingResultVo.AccountProcessingDetail.builder()
                .username(account.getUsername())
                .description(account.getDescription())
                .wardId(wardId)
                .hisDataCount(0)
                .splitDrugCount(0)
                .confirmedCount(0)
                .success(true)
                .build();

        try {
            // 4. 调用东华接口获取住院领药数据（使用指定账号）
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("fyyf", wardId);
            requestParams.put("start_time", startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            requestParams.put("end_time", endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(hangChuangConfig.getInpatientDispenseDetailUrl(), requestParams, "查询住院处方发药");

            // 解析响应结果
            List<InPatientDispenseDetailVo> originalList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseDetailVo.class);

            log.info("杭创接口查询成功，返回{}条数据", originalList.size());
            if (CollectionUtils.isEmpty(originalList)) {
                log.info("时间段 {} - {} 无数据", startTime, endTime);
                return result;
            }
            // 转换为扩展VO并处理追溯码库存信息
            List<InPatientDispenseDetailBindScatteredVo> resultList = new ArrayList<>();
            BeanUtils.copyProperties(originalList, resultList);

            result.setHisDataCount(resultList.size());

            // 4. 筛选拆零药品（trdn_flag=1）
            List<InPatientDispenseDetailBindScatteredVo> splitDrugRecords = resultList.stream()
                    .filter(record -> "1".equals(record.getTrdnFlag()))
                    .collect(Collectors.toList());
            result.setSplitDrugCount(splitDrugRecords.size());

            if (CollectionUtils.isEmpty(splitDrugRecords)) {
                log.info("时间段 {} - {} 无拆零药品", startTime, endTime);
                return result;
            }

            log.info("账号 {} 时间段 {} - {} 获取到拆零药品数量: {}",
                    account.getDescription(), startTime, endTime, splitDrugRecords.size());

            // 7. 分批处理拆零药品（传入账号信息）
            int confirmedCount = processBatchRecords(account, token, splitDrugRecords, ymfUserId);
            result.setConfirmedCount(confirmedCount);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            e.printStackTrace();
            log.error("处理账号 {}({}) 时间段 {} - {} 数据失败",
                    account.getDescription(), account.getUsername(), startTime, endTime, e);
            throw e; // 重新抛出异常，让上层捕获
        }

        return result;
    }
} 