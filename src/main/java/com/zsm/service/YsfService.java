package com.zsm.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.common.exception.BusinessException;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.*;
import com.zsm.mapper.YsfStoTcMapper;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.TableDataInfo;
import com.zsm.model.dto.DispensingRecordDetailQueryDto;
import com.zsm.model.dto.DispensingRecordQueryDto;
import com.zsm.model.dto.TraceabilityUploadDto;
import com.zsm.model.enums.DispenseOrderStatusEnum;
import com.zsm.model.enums.SdTcStatusEnum;
import com.zsm.model.enums.StoTcTaskStatusEnum;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.vo.DispensingRecordDetailVo;
import com.zsm.model.vo.DispensingRecordVo;
import com.zsm.model.vo.SaasUserInfoResponse;
import com.zsm.model.vo.TraceabilityUploadResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新业务服务类
 *
 * <AUTHOR>
 * @date 2025/6/11 15:07
 */
@Slf4j
@Service
public class YsfService {


    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;

    @Resource
    private YsfStoTcTaskSubService ysfStoTcTaskSubService;

    @Resource
    private YsfStoTcMapper ysfStoTcMapper;

    @Resource
    private YsfStoDpsService ysfStoDpsService;

    @Resource
    private YsfStoDpsSubService ysfStoDpsSubService;

    @Resource
    private YsfStoTcService ysfStoTcService;

    @Resource
    private YsfStoTcStatusService ysfStoTcStatusService;

    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;

    @Resource
    private Nhsa3505Service nhsa3505Service;
    @Resource
    private FuYangRenYiAnsycService fuYangRenYiAnsycService;


    /**
     * 上传药品追溯码扫描数据
     *
     * @param request 追溯码上传请求
     * @return 上传结果
     */
    public ApiResult<TraceabilityUploadResultVo> uploadScans(TraceabilityUploadDto request) {
        // 获取token登录的账号信息
        SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
        String userId = userInfo.getUser().getUserId().toString(); // 系统用户ID
        String userName = userInfo.getUser().getUserName(); // 系统用户名
        String orgId = NhsaAccountConstant.getNhsaAccount().getMedicalCode();
        String orgName = NhsaAccountConstant.getNhsaAccount().getMedicalName();

        // 初始化结果变量
        List<String> successIds = new ArrayList<>();
        List<Object> failIds = new ArrayList<>();
        Map<String, String> failMessages = new HashMap<>();

        // 校验请求参数
        if (request == null || request.getPrescriptions() == null || request.getPrescriptions()
                .isEmpty()) {
            return ApiResult.error("请求参数不能为空");
        }

        try {
            // 第一步：校验所有处方数据，收集错误信息
            boolean hasValidationErrors = false;
            for (TraceabilityUploadDto.PrescriptionItem prescription : request.getPrescriptions()) {
                String outPresId = prescription.getOutPresId();
                if (StringUtils.isEmpty(outPresId)) {
                    failIds.add("unknown");
                    failMessages.put("unknown", "处方ID不能为空");
                    hasValidationErrors = true;
                    continue;
                }

                // 校验单个处方
                try {
                    validatePrescription(prescription);
                    successIds.add(outPresId);
                } catch (BusinessException e) {
                    log.error("校验处方[{}]失败: {}", outPresId, e.getMessage());
                    final Object object = e.getObject();
                    failIds.add(object);
                    failMessages.put(outPresId, e.getMessage());
                    hasValidationErrors = true;
                }
            }

            // 构造返回结果
            TraceabilityUploadResultVo result = new TraceabilityUploadResultVo();
            result.setSuccess(successIds);
            result.setFail(failIds);
            result.setFailMessages(failMessages);

            // 第二步：如果有校验错误，直接返回错误结果，不进行任何保存操作
            if (hasValidationErrors) {
                // 清空成功列表，因为有错误就不保存任何数据
                result.setSuccess(Collections.emptyList());
                // 将原本成功的也标记为失败
                for (String successId : successIds) {
                    if (!failIds.contains(successId)) {
                        failIds.add(successId);
                        failMessages.put(successId, "由于其他处方校验失败，该处方未被保存");
                    }
                }
                result.setFail(failIds);
                result.setFailMessages(failMessages);

                ApiResult<TraceabilityUploadResultVo> response = ApiResult.error("部分处方校验失败，所有数据未保存");
                response.setData(result);
                return response;
            }

            // 第三步：所有校验都通过，执行数据保存操作
            List<TraceabilityUploadDto.PrescriptionItem> prescriptionsToProcess = new ArrayList<>();

            for (TraceabilityUploadDto.PrescriptionItem prescription : request.getPrescriptions()) {
                String outPresId = prescription.getOutPresId();
                try {
                    // 处理处方并获取过滤后的处方（移除重复的药品明细）
                    TraceabilityUploadDto.PrescriptionItem filteredPrescription = processPrescription(prescription, userId, userName, orgId, orgName, request);

                    // 如果过滤后的处方还有药品明细，则添加到待处理列表中
                    if (filteredPrescription != null && filteredPrescription.getDrugItems() != null && !filteredPrescription.getDrugItems().isEmpty()) {
                        prescriptionsToProcess.add(filteredPrescription);
                        
                        // 如果过滤后的药品明细数量少于原来的数量，记录日志
                        int originalCount = prescription.getDrugItems().size();
                        int filteredCount = filteredPrescription.getDrugItems().size();
                        if (filteredCount < originalCount) {
                            log.info("处方[{}]原有{}个药品明细，过滤重复后剩余{}个明细进行SaaS扣库存操作", 
                                    outPresId, originalCount, filteredCount);
                        }
                    } else {
                        log.info("处方[{}]所有药品明细都存在重复记录，跳过SaaS扣库存操作", outPresId);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("保存处方[{}]数据失败: {}", outPresId, e.getMessage(), e);
                    // 如果保存阶段出错，抛出异常以触发事务回滚
                    throw new BusinessException("保存处方[" + outPresId + "]数据失败: " + e.getMessage());
                }
            }

            // 处理本地业务后,异步进行处理拆分trdnFlag=1的药品明细，过滤掉重复提交的药品
            for (TraceabilityUploadDto.PrescriptionItem prescription : prescriptionsToProcess) {
                fuYangRenYiAnsycService.saveSassConfirmMedicationDispensingAsync(prescription, userInfo.getAuthorization());
            }

            // 所有操作成功
            return ApiResult.success(result);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("上传药品追溯码失败: {}", e.getMessage(), e);
            throw new BusinessException("上传药品追溯码失败: " + e.getMessage());
        }

    }


    /**
     * 校验处方数据
     *
     * @param prescription 处方信息
     * @throws BusinessException 校验失败时抛出异常
     */
    private void validatePrescription(TraceabilityUploadDto.PrescriptionItem prescription) {
        String outPresId = prescription.getOutPresId();

        // 校验处方基本信息
        if (StringUtils.isEmpty(outPresId)) {
            throw new BusinessException("处方ID不能为空");
        }

        if (prescription.getDrugItems() == null || prescription.getDrugItems()
                .isEmpty()) {
            throw new BusinessException("处方药品明细不能为空");
        }

        // 1. 收集所有追溯码进行批量查询
        List<String> allTraceCodes = new ArrayList<>();
        for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
            if (drugItem.getTrdnFlag() == 1) {
                continue;
            }
            String drugtracinfo = drugItem.getDrugtracinfoScanned();
            if (StringUtils.isNotEmpty(drugtracinfo)) {
                String[] drugtracinfoArray = drugtracinfo.split(",");
                for (String drugZsm : drugtracinfoArray) {
                    if (StringUtils.isNotEmpty(drugZsm.trim())) {
                        allTraceCodes.add(drugZsm.trim());
                    }
                }
            }
        }

        // 2. 批量查询追溯码信息
        Map<String, YsfStoTc> batchTraceCodes = new HashMap<>();
        if (!allTraceCodes.isEmpty()) {
            batchTraceCodes = ysfStoTcMapper.selectBatchByTraceinfo(allTraceCodes);
        }

        // 3. 校验每个药品明细
        for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
            if (drugItem.getTrdnFlag() == 1) {
                continue;
            }
            validateDrugItem(drugItem, batchTraceCodes);
        }
    }

    /**
     * 校验药品明细数据
     *
     * @param drugItem        药品明细
     * @param batchTraceCodes 批量追溯码缓存（用于批量校验优化）
     * @throws BusinessException() 校验失败时抛出异常
     */
    private void validateDrugItem(TraceabilityUploadDto.DrugItem drugItem, Map<String, YsfStoTc> batchTraceCodes) {
        String outPresdetailid = drugItem.getOutPresdetailid();
        String drugtracinfo = drugItem.getDrugtracinfoScanned();

        if (StringUtils.isEmpty(outPresdetailid)) {
            throw new BusinessException("处方明细ID不能为空");
        }

        if (StringUtils.isEmpty(drugtracinfo)) {
            return;
        }

        // 拆零的药品，追溯码校验逻辑不同：
        // - 拆零药品的同一个追溯码可以被多次使用（一盒药拆分给多个患者）
        // - 在processTraceabilityCode方法中会有专门的拆零处理逻辑
        // - 此处跳过常规校验以避免误报已使用的错误
        if (drugItem.getTrdnFlag() == 1) {
            return;
        }

        // 拆分追溯码并校验每个追溯码
        String[] drugtracinfoArray = drugtracinfo.split(",");
        for (String drugZsm : drugtracinfoArray) {
            if (StringUtils.isEmpty(drugZsm.trim())) {
                throw new BusinessException("追溯码不能为空");
            }

            // 批量校验追溯码是否已被使用
            validateTraceabilityCodeBatch(drugZsm.trim(), batchTraceCodes);
        }
    }

    /**
     * 校验追溯码是否可用（单个校验，保留兼容性）
     *
     * @param drugtracinfo 追溯码
     * @throws BusinessException() 校验失败时抛出异常
     */
    private void validateTraceabilityCode(String drugtracinfo) {
        // 查询是否存在追溯码记录
        LambdaQueryWrapper<YsfStoTc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0");
        YsfStoTc stoTc = ysfStoTcService.getOne(queryWrapper);

        if (stoTc != null) {
            // 检查追溯码是否已被使用
            if (stoTc.getAmountRem() != null && stoTc.getAmountRem()
                    .compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("追溯码[" + drugtracinfo + "]已被使用", stoTc);
            }
        }
    }

    /**
     * 批量校验追溯码是否可用
     *
     * @param drugtracinfo    追溯码
     * @param batchTraceCodes 批量追溯码缓存
     * @throws BusinessException() 校验失败时抛出异常
     */
    private void validateTraceabilityCodeBatch(String drugtracinfo, Map<String, YsfStoTc> batchTraceCodes) {
        YsfStoTc stoTc = batchTraceCodes.get(drugtracinfo);

        if (stoTc != null) {
            // 检查追溯码是否已被使用
            if (stoTc.getAmountRem() != null && stoTc.getAmountRem()
                    .compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessException("追溯码[" + drugtracinfo + "]已被使用", stoTc);
            }
        }
    }

    /**
     * 处理单个处方的追溯码上传
     *
     * @param prescription 处方信息
     * @param userId       用户ID
     * @param userName     用户名
     * @param orgId        机构ID
     * @param orgName      机构名称
     * @return 过滤掉重复药品明细后的处方对象，只包含没有重复的药品明细
     */
    private TraceabilityUploadDto.PrescriptionItem processPrescription(TraceabilityUploadDto.PrescriptionItem prescription,
                                        String userId, String userName, String orgId, String orgName, TraceabilityUploadDto request) {
        String outPresId = prescription.getOutPresId();

        // 1. 获取或创建发药单
        YsfStoDps stoDps = getOrCreateStoDps(outPresId, prescription, userName, orgId, orgName, request);

        // 2. 获取或创建扫码任务
        YsfStoTcTask tcTask = getOrCreateTcTask(outPresId, stoDps, userId, userName, orgId, orgName);

        // 3. 更新发药单关联的任务ID
        boolean needUpdate = false;
        if (stoDps.getIdTask() == null) {
            needUpdate = true;
        } else if (!stoDps.getIdTask()
                .equals(tcTask.getIdTask())) {
            needUpdate = true;
        }

        if (needUpdate) {
            stoDps.setIdTask(tcTask.getIdTask());
            ysfStoDpsService.updateById(stoDps);
        }

        // 4. 处理每个药品明细的追溯码，收集没有重复的药品明细
        List<TraceabilityUploadDto.DrugItem> nonDuplicateDrugItems = new ArrayList<>();
        for (TraceabilityUploadDto.DrugItem drugItem : prescription.getDrugItems()) {
            boolean drugItemHasNoDuplicates = processdrugItem(drugItem, stoDps, tcTask, userId, userName, orgId, orgName);
            if (drugItemHasNoDuplicates) {
                nonDuplicateDrugItems.add(drugItem);
            } else {
                log.info("药品明细[{}]存在重复追溯码，将在SaaS扣库存操作中过滤掉", drugItem.getOutPresdetailid());
            }
        }

        // 5. 创建过滤后的处方对象
        if (!nonDuplicateDrugItems.isEmpty()) {
            // 深拷贝处方对象，但只包含没有重复的药品明细
            TraceabilityUploadDto.PrescriptionItem filteredPrescription = new TraceabilityUploadDto.PrescriptionItem();
            // 复制处方基本信息
            filteredPrescription.setOutPresId(prescription.getOutPresId());
            filteredPrescription.setPatId(prescription.getPatId());
            filteredPrescription.setPatName(prescription.getPatName());
            filteredPrescription.setCardNo(prescription.getCardNo());
            filteredPrescription.setIdDept(prescription.getIdDept());
            filteredPrescription.setSendTime(prescription.getSendTime());
            // 设置过滤后的药品明细列表
            filteredPrescription.setDrugItems(nonDuplicateDrugItems);
            
            return filteredPrescription;
        } else {
            return null; // 所有药品明细都有重复，返回null
        }
    }

    /**
     * 获取或创建发药单
     */
    private YsfStoDps getOrCreateStoDps(String outPresId, TraceabilityUploadDto.PrescriptionItem prescription,
                                        String userName, String orgId, String orgName, TraceabilityUploadDto request) {
        // 查询是否存在发药单
        LambdaQueryWrapper<YsfStoDps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoDps::getCfxh, outPresId)
                .eq(YsfStoDps::getDelFlag, "0");
        YsfStoDps stoDps = ysfStoDpsService.getOne(queryWrapper);

        if (stoDps == null) {
            // 创建新的发药单
            stoDps = new YsfStoDps();
            stoDps.setCfxh(outPresId);
            stoDps.setSdDps(request.getSdDps()); // 处方单
            stoDps.setPatientId(prescription.getPatId());
            stoDps.setPsnName(prescription.getPatName());
            stoDps.setCardNo(prescription.getCardNo());
            stoDps.setFgStatus(DispenseOrderStatusEnum.PENDING.getCode()); // 待发药
            stoDps.setFgDps("0"); // 发药单
            stoDps.setFgPrint("0"); // 未打印


            // 设置机构信息
            stoDps.setIdOrg(orgId);
            stoDps.setOrgId(orgId);
            stoDps.setOrgName(orgName);

            // 设置创建和修改信息
            stoDps.setCreateBy(userName);
            stoDps.setCreateTime(LocalDateTime.now());
            stoDps.setUpdateBy(userName);
            stoDps.setUpdateTime(LocalDateTime.now());
            stoDps.setDelFlag("0");

            // 设置发药窗口和工作人员信息
            stoDps.setIdDept(prescription.getIdDept());
            stoDps.setWindow(request.getWindow());
            stoDps.setSelRetnOpterId(request.getSelRetnOpterId());
            stoDps.setPatWardId(request.getPatWardId());
            stoDps.setPatWardName(request.getPatWardName());


            // 根据发药单的send_time字段决定是否更新状态
            if (prescription.getSendTime() != null) {
                // 如果已有发药时间，更新发药单和任务状态为已完成
                stoDps.setFgStatus(DispenseOrderStatusEnum.DISPENSED.getCode());
                stoDps.setSendTime(LocalDateTime.parse(prescription.getSendTime()));
            }

            // 插入数据库
            ysfStoDpsService.save(stoDps);
        }

        return stoDps;
    }

    /**
     * 获取或创建扫码任务
     */
    private YsfStoTcTask getOrCreateTcTask(String outPresId, YsfStoDps stoDps,
                                           String userId, String userName, String orgId, String orgName) {
        YsfStoTcTask tcTask = null;

        // 如果发药单已关联任务ID，则查询任务
        if (stoDps.getIdTask() != null) {
            LambdaQueryWrapper<YsfStoTcTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(YsfStoTcTask::getIdTask, stoDps.getIdTask())
                    .eq(YsfStoTcTask::getDelFlag, "0");
            tcTask = ysfStoTcTaskMapper.selectOne(queryWrapper);

            // 如果任务存在且状态为待处理，直接复用
            if (tcTask != null && TaskStatusEnum.PENDING.getCode()
                    .equals(tcTask.getFgStatus())) {
                return tcTask;
            }
        }

        // 查询是否有其他待处理任务
        LambdaQueryWrapper<YsfStoTcTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTcTask::getCdBiz, outPresId)
                .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.PENDING.getCode()) // 待处理
                .eq(YsfStoTcTask::getDelFlag, "0")
                .orderByDesc(YsfStoTcTask::getCreateTime);
        tcTask = ysfStoTcTaskMapper.selectOne(queryWrapper);

        if (tcTask == null) {
            // 创建新的扫码任务
            tcTask = new YsfStoTcTask();
            tcTask.setCdBiz(outPresId);
            tcTask.setSdTaskType("1"); // 主动任务
            tcTask.setSdTcStatus(SdTcStatusEnum.OUTPATIENT_DISPENSING.getCode()); // 门诊发药
            tcTask.setFgStatus(TaskStatusEnum.PENDING.getCode()); // 待处理
            tcTask.setFgPriority("1"); // 普通优先级
            tcTask.setIdUser(userId);
            tcTask.setIdDept(stoDps.getIdDept());

            // 设置机构信息
            tcTask.setIdOrg(orgId);
            tcTask.setOrgId(orgId);
            tcTask.setOrgName(orgName);

            // 设置创建和修改信息
            tcTask.setCreateBy(userName);
            tcTask.setCreateTime(LocalDateTime.now());
            tcTask.setUpdateBy(userName);
            tcTask.setUpdateTime(LocalDateTime.now());
            tcTask.setDelFlag("0");
            tcTask.setRemark(stoDps.getPsnName());


            // 5. 根据发药单的send_time字段决定是否更新状态
            if (stoDps.getSendTime() != null) {
                // 如果已有发药时间，更新发药单和任务状态为已完成
                tcTask.setFgStatus(TaskStatusEnum.COMPLETED.getCode());
            }

            // 插入数据库
            ysfStoTcTaskMapper.insert(tcTask);
        }

        return tcTask;
    }

    /**
     * 处理药品明细项
     *
     * @return 所有跟踪代码都已成功处理（无重复）则为 true，有任何重复则为 false
     */
    private boolean processdrugItem(TraceabilityUploadDto.DrugItem drugItem, YsfStoDps stoDps, YsfStoTcTask tcTask,
                                    String userId, String userName, String orgId, String orgName) {
        String outPresdetailid = drugItem.getOutPresdetailid();
        String drugtracinfo = drugItem.getDrugtracinfoScanned();

        // 拆分追溯码
        String[] drugtracinfoArray = drugtracinfo.split(",");
        int drugtracinfoArrayLength = drugtracinfoArray.length;
        boolean hasNoDuplicates = true; // 跟踪是否有重复记录

        if (StringUtils.isEmpty(outPresdetailid)) {
            throw new BusinessException("处方明细ID不能为空");
        }

        // 1. 获取或创建扫码任务明细
        YsfStoTcTaskSub tcTaskSub = getOrCreateTcTaskSub(tcTask, drugItem, userName, orgId, orgName);

        // 2. 获取或创建发药单明细
        YsfStoDpsSub stoDpsSub = getOrCreateStoDpsSub(stoDps, drugItem, userId, userName, orgId, orgName);

        // 3. 处理追溯码（商品追溯码主表）一个追溯码一个记录
        for (String drugZsm : drugtracinfoArray) {

            if (StringUtils.isEmpty(drugZsm.trim())) {
                continue;
            }

            boolean wasNewRecord = processTraceabilityCode(drugZsm, drugItem, stoDpsSub.getId()
                    .toString(), userName, orgId, orgName, tcTask.getIdDept());

            if (!wasNewRecord) {
                hasNoDuplicates = false; // 发现重复记录
            }
        }
        // 4. 更新扫码任务明细信息
        tcTaskSub.setDrugtracinfo(drugtracinfo);
        tcTaskSub.setFgScanned("1"); // 已扫码
        tcTaskSub.setScanUser(userName);
        tcTaskSub.setScanTime(LocalDateTime.now());
        tcTaskSub.setUpdateBy(userName);
        tcTaskSub.setUpdateTime(LocalDateTime.now());
        tcTaskSub.setTracCnt(drugtracinfoArrayLength);
        tcTaskSub.setIdBizSub(stoDpsSub.getId());
        ysfStoTcTaskSubService.updateById(tcTaskSub);

        // 5. 更新发药单明细
        stoDpsSub.setDrugtracinfo(drugtracinfo);
        stoDpsSub.setUpdateBy(userName);
        stoDpsSub.setUpdateTime(LocalDateTime.now());
        stoDpsSub.setTracCnt(drugtracinfoArrayLength);
        ysfStoDpsSubService.updateById(stoDpsSub);

        // 6. 更新3505表中的追溯码信息
        nhsa3505Service.updateDrugTraceabilityInfo(drugItem.getOutPresdetailid(), drugtracinfo, userName, orgId, orgName);

        return hasNoDuplicates; // 返回是否没有重复记录
    }

    /**
     * 获取或创建任务明细
     */
    private YsfStoTcTaskSub getOrCreateTcTaskSub(YsfStoTcTask tcTask, TraceabilityUploadDto.DrugItem drugItem,
                                                 String userName, String orgId, String orgName) {
        // 查询是否存在任务明细
        YsfStoTcTaskSub tcTaskSub = ysfStoTcTaskSubService.getByTaskIdAndCfmxxh(
                tcTask.getIdTask()
                        .toString(), drugItem.getOutPresdetailid());

        if (tcTaskSub == null) {
            // 创建新的任务明细
            tcTaskSub = new YsfStoTcTaskSub();
            tcTaskSub.setIdTask(tcTask.getIdTask()
                    .toString());
            tcTaskSub.setCfmxxh(drugItem.getOutPresdetailid());
            tcTaskSub.setDrugCode(drugItem.getDrugCode());
            tcTaskSub.setFgScanned("0"); // 未扫码
            tcTaskSub.setDrugtracinfo(drugItem.getDrugtracinfoScanned());
            tcTaskSub.setQuantity(drugItem.getQuantity());
            tcTaskSub.setUnit(drugItem.getUnit());

            // 设置机构信息
            tcTaskSub.setIdOrg(orgId);
            tcTaskSub.setOrgId(orgId);
            tcTaskSub.setOrgName(orgName);

            // 设置创建和修改信息
            tcTaskSub.setCreateBy(userName);
            tcTaskSub.setCreateTime(LocalDateTime.now());
            tcTaskSub.setUpdateBy(userName);
            tcTaskSub.setUpdateTime(LocalDateTime.now());
            tcTaskSub.setDelFlag("0");
            tcTaskSub.setRemark(tcTask.getRemark());

            // 插入数据库
            ysfStoTcTaskSubService.save(tcTaskSub);
        }

        return tcTaskSub;
    }

    /**
     * 获取或创建发药单明细
     */
    private YsfStoDpsSub getOrCreateStoDpsSub(YsfStoDps stoDps, TraceabilityUploadDto.DrugItem drugItem,
                                              String userId, String userName, String orgId, String orgName) {
        // 查询是否存在发药单明细
        LambdaQueryWrapper<YsfStoDpsSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoDpsSub::getIdDps, stoDps.getIdDps())
                .eq(YsfStoDpsSub::getCfmxxh, drugItem.getOutPresdetailid())
                .eq(YsfStoDpsSub::getDelFlag, "0");
        List<YsfStoDpsSub> stoDpsSubList = ysfStoDpsSubService.list(queryWrapper);
        YsfStoDpsSub stoDpsSub = null;
        if (!stoDpsSubList.isEmpty()) {
            stoDpsSub = stoDpsSubList.get(0);
            if (stoDpsSubList.size() > 1) {
                log.warn("发现{}条相同发药单明细记录: idDps={}, cfmxxh={}, 使用第一条记录ID={}", 
                        stoDpsSubList.size(), stoDps.getIdDps(), drugItem.getOutPresdetailid(), stoDpsSub.getId());
            }
        }

        if (stoDpsSub == null) {
            // 创建新的发药单明细
            stoDpsSub = new YsfStoDpsSub();
            stoDpsSub.setIdDps(stoDps.getIdDps()
                    .toString());
            stoDpsSub.setCfmxxh(drugItem.getOutPresdetailid());
            stoDpsSub.setCfxh(stoDps.getCfxh());
            stoDpsSub.setDrugCode(drugItem.getDrugCode());
            stoDpsSub.setNaFee(drugItem.getDrugName());
            stoDpsSub.setPriceSale(drugItem.getPrice() != null ? BigDecimal.valueOf(drugItem.getPrice()) : null);
            stoDpsSub.setSelRetnCnt(drugItem.getQuantity());
            stoDpsSub.setAmtTotal(drugItem.getAmount() != null ? BigDecimal.valueOf(drugItem.getAmount()) : null);
            stoDpsSub.setAmtTotalDps(drugItem.getAmount() != null ? BigDecimal.valueOf(drugItem.getAmount()) : null);
            stoDpsSub.setUnitSale(drugItem.getUnit());
            stoDpsSub.setUnitSaleFactor(drugItem.getMinDoseCount());
            stoDpsSub.setQuantity(drugItem.getQuantity());
            stoDpsSub.setUnit(drugItem.getUnit());

            // 设置机构信息
            stoDpsSub.setIdOrg(orgId);
            stoDpsSub.setOrgId(orgId);
            stoDpsSub.setOrgName(orgName);

            // 设置创建和修改信息
            stoDpsSub.setCreateBy(userName);
            stoDpsSub.setCreateTime(LocalDateTime.now());
            stoDpsSub.setUpdateBy(userName);
            stoDpsSub.setUpdateTime(LocalDateTime.now());
            stoDpsSub.setDelFlag("0");

            // 插入数据库
            ysfStoDpsSubService.save(stoDpsSub);
        }

        return stoDpsSub;
    }

    /**
     * 处理追溯码（商品追溯码主表）一个追溯码一个记录
     *
     * @param drugtracinfo 追溯码
     * @param drugItem     药品项目
     * @param stoDpsSubId  发药单明细id
     * @param userName     用户名
     * @param orgId        组织id
     * @param orgName      组织名称
     * @param idDept       发药部门id
     * @return true if new record was created, false if it was a duplicate
     */
    private boolean processTraceabilityCode(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem,
                                            String stoDpsSubId, String userName, String orgId, String orgName, String idDept) {

        LambdaQueryWrapper<YsfStoTc> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                    .eq(YsfStoTc::getDelFlag, "0");
        // 拆零的数据不再存存入tc表中,只保存tc_status表的追溯码状态记录
        if (drugItem.getTrdnFlag() == 1) {
            // 先查询数量
            long count = ysfStoTcService.count(queryWrapper);
            if (count >= 1) {
                YsfStoTc stoTc = ysfStoTcService.lambdaQuery()
                .eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0")
                .last("limit 1")
                .one();
                // 创建追溯码状态记录
                return createTraceabilityStatus(drugtracinfo, drugItem, stoDpsSubId, stoTc.getIdTc(), userName, orgId, orgName, idDept);
            }
        }
        // 查询是否存在追溯码记录        
        YsfStoTc stoTc = ysfStoTcService.lambdaQuery()
                .eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0")
                .last("limit 1")
                .one();

        if (stoTc != null) {
            // 检查追溯码是否已被使用
            if (stoTc.getAmountRem() != null && stoTc.getAmountRem()
                    .compareTo(BigDecimal.ZERO) == 0 && drugItem.getTrdnFlag() == 0) {
                throw new BusinessException("追溯码[" + drugtracinfo + "]已被使用");
            }

            // 更新追溯码记录，将剩余数量设为0
            stoTc.setAmountRem(BigDecimal.ZERO);
            stoTc.setUpdateBy(userName);
            stoTc.setUpdateTime(LocalDateTime.now());
            ysfStoTcService.updateById(stoTc);
        } else {
            // 创建新的追溯码记录
            stoTc = new YsfStoTc();
            stoTc.setDrugtracinfo(drugtracinfo);
            stoTc.setDrugCode(drugItem.getDrugCode());
            stoTc.setAmountRem(BigDecimal.ZERO); // 表示已全部消耗
            stoTc.setUnitSaleFactor(drugItem.getMinDoseCount());
            stoTc.setUnitTc(drugItem.getMinPackingName());
            stoTc.setFgActive("1"); // 有效
            stoTc.setIdDept(idDept);

            // 设置机构信息
            stoTc.setIdOrg(orgId);
            stoTc.setOrgId(orgId);
            stoTc.setOrgName(orgName);
            stoTc.setSdTcManage("简易管理");

            // 设置创建和修改信息
            stoTc.setCreateBy(userName);
            stoTc.setCreateTime(LocalDateTime.now());
            stoTc.setUpdateBy(userName);
            stoTc.setUpdateTime(LocalDateTime.now());
            stoTc.setDelFlag("0");

            // 插入数据库
            ysfStoTcService.save(stoTc);
        }

        // 创建追溯码状态记录
        return createTraceabilityStatus(drugtracinfo, drugItem, stoDpsSubId, stoTc.getIdTc(), userName, orgId, orgName, idDept);
    }

    /**
     * 创建追溯码状态记录
     *
     * @param drugtracinfo 追溯码
     * @param drugItem     药品项目
     * @param stoDpsSubId  发药单明细id
     * @param idTc         追溯码id
     * @param userName     用户名
     * @param orgId        组织id
     * @param orgName      组织名称
     * @param idDept       发药部门id
     * @return true if record was created, false if it was a duplicate
     */
    private boolean createTraceabilityStatus(String drugtracinfo, TraceabilityUploadDto.DrugItem drugItem,
                                             String stoDpsSubId, Long idTc, String userName, String orgId, String orgName, String idDept) {
        // 检查是否已存在相同的cfmxxh和drugtracinfo记录
        LambdaQueryWrapper<YsfStoTcStatus> duplicateQuery = new LambdaQueryWrapper<>();
        duplicateQuery.eq(YsfStoTcStatus::getCfmxxh, drugItem.getOutPresdetailid())
                .eq(YsfStoTcStatus::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTcStatus::getDelFlag, "0");

        List<YsfStoTcStatus> existingRecords = ysfStoTcStatusService.list(duplicateQuery);
        if (!existingRecords.isEmpty()) {
            log.warn("发现{}条重复提交的追溯码状态记录: cfmxxh={}, drugtracinfo={}",
                    existingRecords.size(), drugItem.getOutPresdetailid(), drugtracinfo);

            // 使用stream标记所有现有记录为重复提交
            LocalDateTime now = LocalDateTime.now();
            List<YsfStoTcStatus> updatedRecords = existingRecords.stream()
                    .peek(existingRecord -> {
                        existingRecord.setRemark("重复提交检测到于: " + now + " (记录ID: " + existingRecord.getId() + ")");
                        existingRecord.setUpdateBy(userName);
                        existingRecord.setUpdateTime(now);
                        log.warn("已标记重复记录ID={} 为重复提交", existingRecord.getId());
                    })
                    .collect(Collectors.toList());
            
            // 批量更新
            ysfStoTcStatusService.updateBatchById(updatedRecords);

            return false; // 返回false表示是重复记录
        }

        YsfStoTcStatus tcStatus = new YsfStoTcStatus();
        tcStatus.setSdTcStatus(SdTcStatusEnum.OUTPATIENT_DISPENSING.getCode()); // 门诊发药
        tcStatus.setSdTcManage("1"); // 简易管理模式
        tcStatus.setIdBizOri(stoDpsSubId);
        tcStatus.setCfmxxh(drugItem.getOutPresdetailid());
        tcStatus.setDrugCode(drugItem.getDrugCode());
        tcStatus.setDrugtracinfo(drugtracinfo);
        tcStatus.setSdTc("2"); // 商品追溯码
        tcStatus.setSelRetnCnt(drugItem.getQuantity());
        tcStatus.setFgUp("0"); // 未上传
        tcStatus.setFgActive("1"); // 有效
        tcStatus.setIdDept(idDept);
        tcStatus.setIdTc(idTc);
        tcStatus.setFgPack(drugItem.getTrdnFlag().toString());

        // 设置机构信息
        tcStatus.setIdOrg(orgId);
        tcStatus.setOrgId(orgId);
        tcStatus.setOrgName(orgName);

        // 设置创建和修改信息
        tcStatus.setCreateBy(userName);
        tcStatus.setCreateTime(LocalDateTime.now());
        tcStatus.setUpdateBy(userName);
        tcStatus.setUpdateTime(LocalDateTime.now());
        tcStatus.setDelFlag("0");

        // 插入数据库
        ysfStoTcStatusService.save(tcStatus);

        return true; // 返回true表示成功创建新记录
    }

    /**
     * 取消扫码任务
     *
     * @param taskId 任务id
     * @return {@link ApiResult }
     */
    @Transactional(rollbackFor = Exception.class)
    public ApiResult<String> cancelTask(Long taskId) {
        // 查询ysf_sto_tc_task记录，确认状态为'0'(待处理)
        YsfStoTcTask task = ysfStoTcTaskService.getById(taskId);
        if (task == null) {
            return ApiResult.error("任务不存在");
        }

        if (!StoTcTaskStatusEnum.PENDING.getCode()
                .equals(task.getFgStatus())) {
            return ApiResult.error("只能取消待处理状态的任务");
        }

        // 更新ysf_sto_tc_task状态为'2'(已失效)，并添加备注
        task.setFgStatus(StoTcTaskStatusEnum.EXPIRED.getCode());
        task.setMemo("操作员取消，发药前病人未取药");
        ysfStoTcTaskService.updateById(task);

        // 处理已扫描的追溯码ysf_sto_tc
        // 根据被取消的id_task，查询ysf_sto_tc_task_sub获取所有已扫描的drugtracinfo
        LambdaQueryWrapper<YsfStoTcTaskSub> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(YsfStoTcTaskSub::getIdTask, taskId.toString())
                .eq(YsfStoTcTaskSub::getFgScanned, "1")  // 只处理已扫描的明细
                .eq(YsfStoTcTaskSub::getDelFlag, "0");   // 未删除的记录
        List<YsfStoTcTaskSub> taskSubList = ysfStoTcTaskSubService.list(queryWrapper);

        if (!taskSubList.isEmpty()) {
            for (YsfStoTcTaskSub taskSub : taskSubList) {
                String drugtracinfo = taskSub.getDrugtracinfo();

                // 如果追溯码为空，跳过处理
                if (StringUtils.isEmpty(drugtracinfo)) {
                    log.warn("任务明细[{}]的追溯码为空，跳过处理", taskSub.getIdSub());
                    continue;
                }

                // 如果drugtracinfo包含逗号，表示有多个追溯码
                if (drugtracinfo.contains(",")) {
                    String[] arr = drugtracinfo.split(",");
                    for (String zsm : arr) {
                        if (StringUtils.isNotEmpty(zsm.trim())) {
                            this.restoreData(taskSub, zsm.trim());
                        }
                    }
                } else {
                    // 只有单个追溯码时才处理
                    this.restoreData(taskSub, drugtracinfo);
                }
            }
        }

        return ApiResult.success("取消任务成功");
    }

    private void restoreData(YsfStoTcTaskSub taskSub, String drugtracinfo) {
        // 参数校验
        if (StringUtils.isEmpty(drugtracinfo)) {
            log.warn("恢复追溯码时传入的追溯码为空");
            return;
        }

        // 将追溯码可用数量恢复为其包装数量
        LambdaQueryWrapper<YsfStoTc> tcWrapper = new LambdaQueryWrapper<>();
        tcWrapper.eq(YsfStoTc::getDrugtracinfo, drugtracinfo)
                .eq(YsfStoTc::getDelFlag, "0");
        List<YsfStoTc> stoTcList = ysfStoTcService.list(tcWrapper);
        YsfStoTc stoTc = null;
        if (!stoTcList.isEmpty()) {
            stoTc = stoTcList.get(0);
            if (stoTcList.size() > 1) {
                log.warn("恢复追溯码时发现{}条相同记录: drugtracinfo={}, 使用第一条记录ID={}", 
                        stoTcList.size(), drugtracinfo, stoTc.getIdTc());
            }
        }

        if (stoTc != null) {
            // 恢复其tc表包装数量
            Integer unitSaleFactor = stoTc.getUnitSaleFactor();
            if (unitSaleFactor != null) {
                stoTc.setAmountRem(new BigDecimal(unitSaleFactor));
            } else {
                log.warn("追溯码[{}]的包装数量为空，设置默认值1", drugtracinfo);
                stoTc.setAmountRem(BigDecimal.ONE);
            }
            ysfStoTcService.updateById(stoTc);

            // 创建ysf_sto_tc_status记录
            YsfStoTcStatus tcStatus = new YsfStoTcStatus();
            tcStatus.setSdTcStatus(SdTcStatusEnum.CANCEL_TASK.getCode());  // 扫码任务取消
            tcStatus.setSdTcManage(stoTc.getSdTcManage());
            tcStatus.setIdBizOri(taskSub.getIdSub()
                    .toString());  // 关联任务明细ID
            tcStatus.setIdTc(stoTc.getIdTc());
            tcStatus.setCfmxxh(taskSub.getCfmxxh());
            tcStatus.setDrugCode(taskSub.getDrugCode());
            tcStatus.setDrugtracinfo(drugtracinfo);
            tcStatus.setIdStoInv(stoTc.getIdStoInv());
            tcStatus.setIdDept(stoTc.getIdDept());
            tcStatus.setSdTc(stoTc.getSdTc());
            tcStatus.setSelRetnCnt(unitSaleFactor != null ? unitSaleFactor : 1);  // 恢复的数量
            tcStatus.setFgActive("1");
            tcStatus.setIdOrg(stoTc.getIdOrg());
            tcStatus.setOrgId(stoTc.getOrgId());
            tcStatus.setOrgName(stoTc.getOrgName());
            tcStatus.setSdTc("2");

            ysfStoTcStatusService.save(tcStatus);
        } else {
            log.warn("无法找到追溯码[{}]的记录，跳过恢复操作", drugtracinfo);
        }
    }


    /**
     * 根据条件查询发药单列表 (分页)
     *
     * @param queryDto 查询参数
     * @return 分页后的发药单列表, 包含 DispensingRecordVo 对象
     */
    public TableDataInfo queryDispensingRecords(DispensingRecordQueryDto queryDto) {
        // 1. 设置分页参数
        int pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
        int pageSize = queryDto.getPageSize() == null ? 10 : queryDto.getPageSize();
        Page<YsfStoDps> page = new Page<>(pageNum, pageSize);

        // 2. 构建查询条件 (MyBatis LambdaQueryWrapper)
        LambdaQueryWrapper<YsfStoDps> dpsQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(queryDto.getPatientName())) {
            dpsQueryWrapper.like(YsfStoDps::getPsnName, queryDto.getPatientName());
        }
        if (StringUtils.isNotEmpty(queryDto.getPrescriptionId())) {
            // 根据需求文档，处方号可以对应 outPresId 或 cfxh。
            // YsfStoDps 实体中似乎只有 cfxh。如果 outPresId 是另一个表的字段或不同含义，需确认。
            // 这里我们假设 DTO 中的 prescriptionId 直接对应 YsfStoDps 的 cfxh。
            dpsQueryWrapper.eq(YsfStoDps::getCfxh, queryDto.getPrescriptionId());
        }
        if (StringUtils.isNotEmpty(queryDto.getDispensingStatus())) {
            dpsQueryWrapper.eq(YsfStoDps::getFgStatus, queryDto.getDispensingStatus());
        }
        if (StringUtils.isNotEmpty(queryDto.getDeptId())) {
            dpsQueryWrapper.eq(YsfStoDps::getIdDept, queryDto.getDeptId());
        }
        if (StringUtils.isNotEmpty(queryDto.getPatWardId())) {
            dpsQueryWrapper.eq(YsfStoDps::getPatWardId, queryDto.getPatWardId());
        }
        if (StringUtils.isNotEmpty(queryDto.getSdDps())) {
            dpsQueryWrapper.eq(YsfStoDps::getSdDps, queryDto.getSdDps());
        }
        if (queryDto.getStartTime() != null) {
            dpsQueryWrapper.ge(YsfStoDps::getSendTime, queryDto.getStartTime());
        }
        if (queryDto.getEndTime() != null) {
            // 对于日期范围查询，通常结束时间应该包含当天，所以可以用小于第二天的开始
            // 或者数据库层面处理 DATE(send_time) <= DATE(queryDto.getEndTime())
            // 为简单起见，这里用 le，如果需要精确到秒，前端传入的时间应为当天的 23:59:59
            dpsQueryWrapper.le(YsfStoDps::getSendTime, queryDto.getEndTime());
        }
        dpsQueryWrapper.eq(YsfStoDps::getFgDps, queryDto.getFgDps());
        dpsQueryWrapper.eq(YsfStoDps::getDelFlag, "0"); // 通常查询未删除的记录
        dpsQueryWrapper.orderByDesc(YsfStoDps::getSendTime, YsfStoDps::getIdDps); // 按发药时间降序, ID降序作为次排序

        // 3. 执行分页查询
        IPage<YsfStoDps> pageResult = ysfStoDpsService.page(page, dpsQueryWrapper);

        // 4. 如果查询结果为空，直接返回
        if (pageResult.getRecords()
                .isEmpty()) {
            return new TableDataInfo(Collections.emptyList(), 0);
        }

        // 5. 遍历查询结果，对每个 YsfStoDps 对象进行处理和转换
        // 5. 批量查询任务映射，避免 N+1 查询问题
        List<String> cfxhList = pageResult.getRecords().stream()
                .map(YsfStoDps::getCfxh)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());

        Map<String, YsfStoTcTask> taskMap;
        if (!cfxhList.isEmpty()) {
            List<YsfStoTcTask> taskList = ysfStoTcTaskMapper.selectLatestByCfxh(cfxhList);
            taskMap = taskList.stream()
                    .collect(Collectors.toMap(YsfStoTcTask::getCdBiz, task -> task, (o, n) -> n));
        } else {
            taskMap = new HashMap<>();
        }

        List<DispensingRecordVo> voList = pageResult.getRecords()
                .stream()
                .map(dps -> {

                    DispensingRecordVo vo = new DispensingRecordVo();

                    BeanUtils.copyProperties(dps, vo);

                    vo.setDispensingFgStatus(dps.getFgStatus());

                    if (StringUtils.isNotEmpty(dps.getCfxh()) && taskMap.containsKey(dps.getCfxh())) {
                        YsfStoTcTask latestTask = taskMap.get(dps.getCfxh());
                        vo.setTaskStatus(latestTask.getFgStatus());
                        vo.setTaskId(latestTask.getIdTask());
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        // 6. 返回分页结果
        return new TableDataInfo(voList, (int) pageResult.getTotal());
    }

    /**
     * 根据条件查询发药单明细列表 (分页)
     *
     * @param queryDto 查询参数
     * @return 分页后的发药单明细列表
     */
    public TableDataInfo queryDispensingRecordDetails(DispensingRecordDetailQueryDto queryDto) {
        // 验证必要参数：cfxh 和 idDps 不能都为空
        if (StringUtils.isEmpty(queryDto.getCfxh()) && queryDto.getIdDps() == null) {
            throw new BusinessException("处方序号(cfxh)和发药单ID(idDps)不能都为空");
        }

        // 1. 设置分页参数
        int pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
        int pageSize = queryDto.getPageSize() == null ? 10 : queryDto.getPageSize();
        Page<YsfStoDpsSub> page = new Page<>(pageNum, pageSize);

        // 2. 构建查询条件
        LambdaQueryWrapper<YsfStoDpsSub> queryWrapper = new LambdaQueryWrapper<>();

        // 处方序号条件
        if (StringUtils.isNotEmpty(queryDto.getCfxh())) {
            queryWrapper.eq(YsfStoDpsSub::getCfxh, queryDto.getCfxh());
        }

        // 发药单ID条件
        if (queryDto.getIdDps() != null) {
            queryWrapper.eq(YsfStoDpsSub::getIdDps, queryDto.getIdDps()
                    .toString());
        }

        // 患者姓名条件（模糊匹配）
        if (StringUtils.isNotEmpty(queryDto.getPatientName())) {
            // 需要关联发药单表查询患者姓名，这里先跳过，或者可以通过子查询实现
            // queryWrapper.like(YsfStoDpsSub::getPatientName, queryDto.getPatientName());
        }

        // 药品名称或编码条件（模糊匹配）
        if (StringUtils.isNotEmpty(queryDto.getDrugNameOrCode())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(YsfStoDpsSub::getNaFee, queryDto.getDrugNameOrCode())
                    .or()
                    .like(YsfStoDpsSub::getDrugCode, queryDto.getDrugNameOrCode())
            );
        }

        queryWrapper.eq(YsfStoDpsSub::getDelFlag, "0"); // 查询未删除的记录
        queryWrapper.orderByAsc(YsfStoDpsSub::getCfmxxh); // 按处方明细序号排序

        // 3. 执行分页查询
        IPage<YsfStoDpsSub> pageResult = ysfStoDpsSubService.page(page, queryWrapper);

        List<YsfStoDpsSub> subList = pageResult.getRecords();
        if (subList.isEmpty()) {
            return new TableDataInfo(Collections.emptyList(), 0);
        }


        // 查询当前活动任务
        LambdaQueryWrapper<YsfStoTcTask> taskQuery = new LambdaQueryWrapper<>();
        // 获取发药单的处方号
        String cfxh = "";
        if (!subList.isEmpty()) {
            cfxh = subList.get(0)
                    .getCfxh();
        }

        taskQuery.eq(YsfStoTcTask::getCdBiz, cfxh)
                .eq(YsfStoTcTask::getDelFlag, "0")
                .eq(YsfStoTcTask::getFgStatus, TaskStatusEnum.PENDING.getCode()) // 待处理的任务
                .orderByDesc(YsfStoTcTask::getCreateTime)
                .last("LIMIT 1");

        YsfStoTcTask currentTask = ysfStoTcTaskMapper.selectOne(taskQuery);

        // 转换为VO对象
        List<DispensingRecordDetailVo> voList = subList.stream()
                .map(sub -> {
                    DispensingRecordDetailVo vo = new DispensingRecordDetailVo();
                    BeanUtils.copyProperties(sub, vo);

                    // 设置任务明细信息
                    YsfStoTcTaskSub taskSub = ysfStoTcTaskSubService.lambdaQuery()
                            .eq(YsfStoTcTaskSub::getCfmxxh, sub.getCfmxxh())
                            .one();
                    if (taskSub != null) {
                        vo.setIdSub(taskSub.getIdSub());
                        vo.setFgScanned(taskSub.getFgScanned());
                    } else {
                        vo.setFgScanned("0"); // 默认未扫码
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        // 如果指定了是否已采集条件，但没有活动任务，则根据条件过滤结果
        if (StringUtils.isNotEmpty(queryDto.getIsCollected()) && currentTask == null) {
            if ("1".equals(queryDto.getIsCollected())) {
                // 如果查询已采集但没有任务，返回空列表
                return new TableDataInfo(Collections.emptyList(), 0);
            }
        }

        return new TableDataInfo(voList, (int) pageResult.getTotal());
    }
}
