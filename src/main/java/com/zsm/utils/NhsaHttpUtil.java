package com.zsm.utils;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zsm.common.exception.BusinessException;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.nhsa.request.Arg0;
import com.zsm.model.nhsa.request.NhsaCityRequest;
import com.zsm.model.nhsa.request.fsi3505.Fsi3505;
import com.zsm.model.nhsa.request.fsi3505.Selinfo3505;
import com.zsm.model.nhsa.request.fsi3506.Fsi3506;
import com.zsm.model.nhsa.request.fsi3506.Selinfo3506;
import com.zsm.model.nhsa.request.fsi3507.Fsi3507;
import com.zsm.model.nhsa.request.fsi3513.Fsi3513;
import com.zsm.model.nhsa.request.fsi3513.Fsi3513Data;
import com.zsm.model.nhsa.request.fsi9001.Fsi9001;
import com.zsm.model.nhsa.request.fsi9001.SignIn;
import com.zsm.model.nhsa.response.Fsi9001Response;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 医保局接口调用工具类
 *
 * <AUTHOR>
 * @date 2025/06/03
 */
@Slf4j
public class NhsaHttpUtil {
/**
     * 常量定义
     */
    private static class Constants {
        // 接口路径
        public static final String DOWNLOAD_URL = "/fsi/api/fileupload/download";
        public static final String UPLOAD_URL = "/fsi/api/fileupload/upload";

        public static final String HSA_FSI_9001 = "/hsa-fsi-9001";
        public static final String HSA_FSI_9002 = "/hsa-fsi-9002";
        public static final String HSA_FSI_3501 = "/hsa-fsi-3501";
        public static final String HSA_FSI_3501A = "/hsa-fsi-3501a";
        public static final String HSA_FSI_3502 = "/hsa-fsi-3502";
        public static final String HSA_FSI_3503 = "/hsa-fsi-3503";
        public static final String HSA_FSI_3504 = "/hsa-fsi-3504";
        public static final String HSA_FSI_3505 = "/hsa-fsi-3505";
        public static final String HSA_FSI_3506 = "/hsa-fsi-3506";
        public static final String HSA_FSI_3507 = "/hsa-fsi-3507";
        public static final String HSA_FSI_3512 = "/hsa-fsi-3512";
        public static final String HSA_FSI_3513 = "/hsa-fsi-3513";
        public static final String HSA_FSI_1301 = "/hsa-fsi-1301";

        // 系统配置
        public static final String IP = "***********";
        public static final String MAC = "94-C6-91-23-04-64";
        public static final Integer TIME_OUT = 30000;

        // 时间格式
        public static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmss";
        public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

        // 接口参数
        public static final String RECER_SYS_CODE = "1";
        public static final String INF_VER = "V1.0";
        public static final String OPTER_TYPE = "1";

        // 响应状态码
        public static final int SUCCESS_CODE = 200;
        public static final int SUCCESS_INF_CODE = 0;
    }


    /**
     * 设置请求基础信息
     *
     * @param infno       接口编号
     * @param arg0        请求参数对象
     * @param signNo      签到令牌
     * @param nhsaAccount 医保账户信息
     */
    public static void setArg0BaseInfo(String infno, Arg0 arg0, String signNo, NhsaAccount nhsaAccount) {
        SimpleDateFormat timestampFormat = new SimpleDateFormat(Constants.TIMESTAMP_FORMAT);
        SimpleDateFormat datetimeFormat = new SimpleDateFormat(Constants.DATETIME_FORMAT);
        Date now = new Date();

        arg0.setInfno(infno);
        arg0.setMsgid(nhsaAccount.getMedicalCode() + timestampFormat.format(now));
        arg0.setMdtrtarea_admvs(nhsaAccount.getArea());
        arg0.setInsuplc_admdvs(nhsaAccount.getArea());
        arg0.setRecer_sys_code(Constants.RECER_SYS_CODE);
        arg0.setInfver(Constants.INF_VER);
        arg0.setOpter_type(Constants.OPTER_TYPE);
        arg0.setOpter(nhsaAccount.getOperatorNo());
        arg0.setOpter_name(nhsaAccount.getOperatorName());
        arg0.setInf_time(datetimeFormat.format(now));
        arg0.setFixmedins_code(nhsaAccount.getMedicalCode());
        arg0.setFixmedins_name(nhsaAccount.getMedicalName());
        arg0.setSign_no(signNo);
    }

    // =============== 业务接口方法 ===============

    /**
     * 9001-签到接口
     *
     * @param nhsaAccount 医保账户信息
     * @return 签到响应结果
     */
    public static NhsaCityResponse<Fsi9001Response> fsi9001(NhsaAccount nhsaAccount) {
        SignIn signIn = new SignIn(Constants.IP, Constants.MAC, nhsaAccount.getOperatorNo());
        Arg0<Fsi9001> arg0 = new Arg0<>();
        setArg0BaseInfo("9001", arg0, "", nhsaAccount);
        arg0.setInput(new Fsi9001(signIn));
        NhsaCityRequest<Fsi9001> request = new NhsaCityRequest<>(arg0);
        return executeRequest("9001", Constants.HSA_FSI_9001, request, nhsaAccount, new TypeReference<NhsaCityResponse<Fsi9001Response>>() {
        });
    }

    /**
     * 3505-医疗保障基金结算清单信息查询
     *
     * @param signNo      签到令牌
     * @param selinfo3505 查询条件
     * @param nhsaAccount 医保账户信息
     * @return 查询响应结果
     */
    public static NhsaCityResponse fsi3505(String signNo, Selinfo3505 selinfo3505, NhsaAccount nhsaAccount) {
        Arg0<Fsi3505> arg0 = new Arg0<>();
        setArg0BaseInfo("3505", arg0, signNo, nhsaAccount);
        arg0.setInput(Fsi3505.builder().selinfo(selinfo3505).build());
        NhsaCityRequest<Fsi3505> request = new NhsaCityRequest<>(arg0);
        return executeRequest("3505", Constants.HSA_FSI_3505, request, nhsaAccount, new TypeReference<NhsaCityResponse<Object>>() {
        });
    }

    /**
     * 3506-医疗保障基金结算清单信息上传
     *
     * @param signNo      签到令牌
     * @param selinfo3506 上传数据
     * @param nhsaAccount 医保账户信息
     * @return 上传响应结果
     */
    public static NhsaCityResponse fsi3506(String signNo, Selinfo3506 selinfo3506, NhsaAccount nhsaAccount) {
        Arg0<Fsi3506> arg0 = new Arg0<>();
        setArg0BaseInfo("3506", arg0, signNo, nhsaAccount);
        arg0.setInput(Fsi3506.builder().selinfo(selinfo3506).build());
        NhsaCityRequest<Fsi3506> request = new NhsaCityRequest<>(arg0);
        return executeRequest("3506", Constants.HSA_FSI_3506, request, nhsaAccount, new TypeReference<NhsaCityResponse<Object>>() {
        });
    }

    /**
     * 3507-医疗保障基金结算清单信息删除
     *
     * @param request     请求对象
     * @param nhsaAccount 医保账户信息
     * @return 删除响应结果
     */
    public static NhsaCityResponse fsi3507(NhsaCityRequest<Fsi3507> request, NhsaAccount nhsaAccount) {
        return executeRequest("3507", Constants.HSA_FSI_3507, request, nhsaAccount, new TypeReference<NhsaCityResponse<Object>>() {
        });
    }

    /**
     * 3513-结算清单信息撤销
     *
     * @param signNo      签到令牌
     * @param fsi3513Data 撤销数据
     * @param nhsaAccount 医保账户信息
     * @return 撤销响应结果
     */
    public static NhsaCityResponse fsi3513(String signNo, Fsi3513Data fsi3513Data, NhsaAccount nhsaAccount) {
        Arg0<Fsi3513> arg0 = new Arg0<>();
        setArg0BaseInfo("3513", arg0, signNo, nhsaAccount);
        arg0.setInput(Fsi3513.builder().data(fsi3513Data).build());
        NhsaCityRequest<Fsi3513> request = new NhsaCityRequest<>(arg0);
        return executeRequest("3513", Constants.HSA_FSI_3513, request, nhsaAccount, new TypeReference<NhsaCityResponse<Object>>() {
        });
    }

    // =============== 通用HTTP请求处理方法 ===============

    /**
     * 执行HTTP请求的通用方法
     *
     * @param interfaceCode 接口编码
     * @param urlPath       接口路径
     * @param request       请求对象
     * @param nhsaAccount   医保账户信息
     * @param typeReference 响应类型
     * @param <T>           请求类型
     * @param <R>           响应类型
     * @return 响应结果
     */
    private static <T, R> R executeRequest(String interfaceCode, String urlPath,
                                           NhsaCityRequest<T> request, NhsaAccount nhsaAccount,
                                           TypeReference<R> typeReference) {
        String logPrefix = "fsi" + interfaceCode + ":";
        String url = nhsaAccount.getBaseUrl() + urlPath;

        // 记录请求日志
        log.info("{}请求地址：{}", logPrefix, url);
        log.info("{}请求数据：{}", logPrefix, JSONUtil.toJsonStr(request));

        try {
            // 发送HTTP请求
            String responseBody = HttpRequest.post(url)
                    .timeout(Constants.TIME_OUT)
                    .body(JSONUtil.toJsonStr(request))
                    .execute()
                    .body();

            // 记录响应日志
            log.info("{}响应数据：{}", logPrefix, responseBody);

            // 解析响应结果
            JSONObject responseJson = JSONUtil.parseObj(responseBody);

            // 检查响应状态
            validateResponse(logPrefix, responseJson, request, responseBody);

            // 转换并返回结果
            return JSONUtil.toBean(responseBody, typeReference, false);

        } catch (Exception e) {
            log.error("{}请求异常：{}", logPrefix, e.getMessage(), e);
            throw new BusinessException("医保接口调用失败：" + e.getMessage(), e);
        }
    }

    /**
     * 验证响应结果
     *
     * @param logPrefix    日志前缀
     * @param responseJson 响应JSON对象
     * @param request      原始请求
     * @param responseBody 响应体字符串
     */
    private static void validateResponse(String logPrefix, JSONObject responseJson,
                                       Object request, String responseBody) {
        // 检查HTTP状态码
        if (responseJson.getInt("code", -1) != Constants.SUCCESS_CODE) {
            log.error("{}HTTP状态码异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
            log.error("{}HTTP状态码异常，返回参数：{}", logPrefix, responseBody);
            throw new BusinessException("医保接口返回HTTP状态码异常");
        }

        // 检查业务状态码
        if (responseJson.containsKey("body")) {
            JSONObject bodyJson = responseJson.getJSONObject("body");
            if (bodyJson != null && bodyJson.getInt("infcode", -1) != Constants.SUCCESS_INF_CODE) {
                log.error("{}业务状态码异常，请求参数：{}", logPrefix, JSONUtil.toJsonStr(request));
                log.error("{}业务状态码异常，返回参数：{}", logPrefix, responseBody);
                throw new BusinessException("医保接口返回业务状态码异常：" + bodyJson.getStr("err_msg", "未知错误"));
            }
        }
    }

}
